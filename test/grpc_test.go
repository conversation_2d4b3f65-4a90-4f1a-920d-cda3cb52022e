package test

import (
	"context"
	"log"
	"net"
	"testing"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/test/bufconn"

	"poc-finder/config"
	grpc2 "poc-finder/pkg/grpc"

	pb "poc-finder/proto"
)

const bufSize = 1024 * 1024

var lis *bufconn.Listener

// setupTestServer 设置测试用的gRPC服务器
func setupTestServer(t *testing.T) (*grpc.Server, pb.VulnPusherClient) {
	// 创建内存监听器
	lis = bufconn.Listen(bufSize)

	// 加载测试配置
	cfg := config.NewConfig()
	if err := cfg.Load("../config/config.toml"); err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 创建gRPC服务器
	server := grpc.NewServer()

	// 创建漏洞处理器
	vulnHandler := grpc2.NewIntegratedVulnHandler(cfg)

	// 注册服务
	vulnPusherServer := grpc2.NewVulnPusherServer(vulnHandler)
	pb.RegisterVulnPusherServer(server, vulnPusherServer)

	// 启动服务器
	go func() {
		if err := server.Serve(lis); err != nil {
			log.Fatalf("服务器启动失败: %v", err)
		}
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 创建客户端连接
	// 使用新的辅助函数处理gRPC连接，自动处理新旧API兼容性
	opts := grpc2.TestClientOptions("bufnet", bufDialer)

	conn, err := grpc2.NewClientConnection(opts)
	if err != nil {
		t.Fatalf("创建客户端连接失败: %v", err)
	}

	client := pb.NewVulnPusherClient(conn)
	return server, client
}

func bufDialer(context.Context, string) (net.Conn, error) {
	return lis.Dial()
}

// TestPushInitial 测试初始化推送
func TestPushInitial(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	req := &pb.PushInitialRequest{
		InitialMessage: &pb.InitialMessage{
			Version:   "1.0.0",
			VulnCount: 10,
			Interval:  "1h",
			Provider: []*pb.Provider{
				{
					Name:        "test_provider",
					DisplayName: "Test Provider",
					Link:        "https://test.com",
				},
			},
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushInitial(ctx, req)
	if err != nil {
		t.Fatalf("PushInitial 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushInitial 成功: %s", resp.Message)
}

// TestPushVuln_Normal 测试普通漏洞推送
func TestPushVuln_Normal(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			UniqueKey:    "test-vuln-001",
			Title:        "Test Vulnerability",
			Description:  "This is a test vulnerability for normal processing",
			Severity:     "High",
			Cve:          "CVE-2024-0001",
			Disclosure:   "2024-01-01",
			Solutions:    "Update to latest version",
			GithubSearch: []string{"CVE-2024-0001", "test vulnerability"},
			References:   []string{"https://example.com/vuln"},
			Tags:         []string{"web", "rce"}, // 普通标签，应该触发所有处理器
			From:         "test_source",
			Reason:       []string{"automated_detection"},
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushVuln (普通漏洞) 成功: %s", resp.Message)
}

// TestPushVuln_NucleiTemplate 测试Nuclei模板漏洞推送
func TestPushVuln_NucleiTemplate(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			UniqueKey:   "nuclei-template-001",
			Title:       "Nuclei Template Vulnerability",
			Description: "This vulnerability should only trigger Nuclei handler",
			Severity:    "Medium",
			Cve:         "CVE-2024-0002",
			Tags:        []string{"nuclei-templates", "web"}, // 包含nuclei-templates标签
			From:        "nuclei_source",
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushVuln (Nuclei模板) 成功: %s", resp.Message)
}

// TestPushText 测试文本推送
func TestPushText(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.PushTextRequest{
		Message:   "Test text message from WatchVuln",
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushText(ctx, req)
	if err != nil {
		t.Fatalf("PushText 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushText 成功: %s", resp.Message)
}

// TestPushVuln_ErrorHandling 测试错误处理
func TestPushVuln_ErrorHandling(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 测试空漏洞信息
	req := &pb.PushVulnRequest{
		VulnInfo:  nil, // 空的漏洞信息
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	// 应该返回失败
	if resp.Success {
		t.Errorf("期望 success=false (因为漏洞信息为空), 实际得到 success=%v", resp.Success)
	}

	t.Logf("错误处理测试成功: %s", resp.Message)
}

// TestPushVuln_MissingIdentifiers 测试缺少标识符的情况
func TestPushVuln_MissingIdentifiers(t *testing.T) {
	server, client := setupTestServer(t)
	defer server.Stop()

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			// 缺少 UniqueKey, Title, CVE 等关键标识符
			Description: "Vulnerability without identifiers",
			Severity:    "Low",
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	// 应该返回失败
	if resp.Success {
		t.Errorf("期望 success=false (因为缺少标识符), 实际得到 success=%v", resp.Success)
	}

	t.Logf("缺少标识符测试成功: %s", resp.Message)
}
