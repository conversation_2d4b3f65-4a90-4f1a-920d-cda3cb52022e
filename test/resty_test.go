package test

import (
	"context"
	"crypto/tls"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/corpix/uarand"
	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/semaphore"

	"poc-finder/model"
)

type repoInfo struct {
	UserName       string
	RepositoryName string
	Description    string
	ReadMe         string
	Trees          string

	Answer   string
	HasPOC   bool
	FileName string
	FileLink string
	Reason   string
}

func Test_RestyGithub(t *testing.T) {
	targetAPI := "https://api.github.com/search/repositories?q={CVEID}+in:name,description,readme&sort=created&order=desc&per_page=10"
	//targetAPI := "https://api.github.com/search/code?q={CVEID}+repo:projectdiscovery/nuclei-templates"
	// rawGithubURL := "https://raw.githubusercontent.com/"
	CVEID := "CVE-2022-21371"
	//CVEID := "PhotoPrism - Default Login"
	//CVEID = strings.Replace(CVEID, " -", "", -1)
	//CVEID = url.QueryEscape(CVEID)

	r := resty.New()
	r.SetTimeout(9 * time.Second).SetRetryCount(3).SetProxy("http://127.0.0.1:8080").SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	r.SetBaseURL(targetAPI)
	r.SetPathParam("CVEID", CVEID)
	r.SetHeader("Accept", "application/vnd.github.v3+json")
	r.SetHeader("User-Agent", uarand.GetRandom())
	r.SetAuthScheme("Bearer").SetAuthToken("*********************************************************************************************")
	resp, err := r.R().Get(r.BaseURL)
	if err != nil {
		t.Fatal(err)
	}
	//t.Log(resp.String())
	var dataSource = new(model.GithubData)
	if err = r.JSONUnmarshal(resp.Body(), &dataSource); err != nil {
		t.Fatal(err)
	}
	// 分别请求不同的数据
	var (
		wg   sync.WaitGroup
		sema = semaphore.NewWeighted(int64(5))
		// GitHub raw 和 API 前缀
		rawURL = "https://raw.githubusercontent.com/"
		apiURL = "https://api.github.com/repos/"
	)
	// 用于保存最终结果
	results := make(map[string]*repoInfo)
	var mu sync.Mutex
	for _, item := range dataSource.Items {
		// 获取分支、树、README 都在同一个 goroutine 内完成
		wg.Add(1)

		// 只有获取到信号量（即没超过 maxWorkers）才会启动下一个 goroutine
		if err = sema.Acquire(context.Background(), 1); err != nil {
			slog.Error("semaphore acquire failed", "err", err)
			wg.Done()
			continue
		}

		go func(item model.Item) {
			defer wg.Done()
			defer sema.Release(1)

			fullName := item.FullName
			// —— 第一步：获取仓库名、仓库描述、用户名 —— //
			mu.Lock()
			results[fullName] = &repoInfo{Description: item.Description}
			ss := strings.Split(fullName, "/")
			if len(ss) != 2 {
				t.Error("invalid repo name")
				results[fullName].UserName = fullName
				results[fullName].RepositoryName = fullName
			}
			results[fullName].UserName = ss[0]
			results[fullName].RepositoryName = ss[1]
			mu.Unlock()
			// —— 第二步：获取 branches 列表 —— //
			branchesURL, found := strings.CutSuffix(item.BranchesURL, "{/branch}")
			if !found {
				slog.Error("didn't find branch URL", "full_name", fullName)
				return
			}

			branchResp, err := r.R().Get(branchesURL)
			if err != nil {
				slog.Error("get branch list failed", "full_name", fullName, "err", err)
				return
			}
			var branches model.Branches
			if err := r.JSONUnmarshal(branchResp.Body(), &branches); err != nil {
				slog.Error("unmarshal branches failed", "full_name", fullName, "err", err)
				return
			}

			// 默认用 main，若有分支则用第一个分支名称
			branchName := "main"
			if len(branches) > 0 {
				branchName = branches[0].Name
			}

			// 在 map 中先生成一个空的 repoInfo
			mu.Lock()
			results[fullName] = &repoInfo{}
			mu.Unlock()

			// —— 第三步：获取树 (trees) —— //
			treeURL := fmt.Sprintf("%s%s/git/trees/%s?recursive=1", apiURL, fullName, branchName)
			treeResp, err := r.R().Get(treeURL)
			var readmePath string
			if err != nil {
				slog.Error("request trees failed", "full_name", fullName, "err", err)
			} else {
				// 解析 JSON，寻找 README 路径（可选：有些仓库 README 不在根目录，这里简单示意）
				var treesData model.Trees
				if err := r.JSONUnmarshal(treeResp.Body(), &treesData); err != nil {
					slog.Error("unmarshal trees JSON failed", "full_name", fullName, "err", err)
				} else {
					strBuilder := strings.Builder{}
					for _, entry := range treesData.Tree {
						info := fmt.Sprintf("Path: %s\nURL: %s\nSize:%d\n\n", entry.Path, entry.URL, entry.Size)
						strBuilder.WriteString(info)
						if strings.EqualFold(entry.Path, "README.md") {
							// 找到根目录下的 README.md
							readmePath = entry.Path
						}
					}
					fileInfo := strBuilder.String()
					mu.Lock()
					results[fullName].Trees = fileInfo
					mu.Unlock()
				}
			}

			// —— 第四步：获取 README.md —— //
			// 如果在上面找到了特定路径，这里也可以拼接自定义相对路径
			readmeURL := fmt.Sprintf("%s%s/%s/%s", rawURL, fullName, branchName, readmePath)
			r.R().SetResponseBodyLimit(1024 * 1024)
			readMeResp, err := r.R().Get(readmeURL)
			if err != nil {
				slog.Error("request README.md failed", "full_name", fullName, "err", err)
			} else {
				readMeStr := readMeResp.String()
				mu.Lock()
				results[fullName].ReadMe = readMeStr
				mu.Unlock()
			}
		}(item)
	}

	// 等待所有仓库都处理完
	wg.Wait()

	res := make(map[string]string)
	sema = semaphore.NewWeighted(int64(3))
	for _, info := range results {
		answer, err := Query("openrouter", "openai/gpt-4o-mini", CVEID, info.UserName, info.RepositoryName, info.Description, info.Trees, info.ReadMe)
		if err != nil {
			t.Fatal(err)
		}
		slog.Debug(answer)
		/*_ = sema.Acquire(context.Background(), 1)
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer sema.Release(1)
			// llama3.1:8b
			answer, err := Query("openrouter", "openai/gpt-4o-mini", CVEID, info.UserName, info.RepositoryName, info.Description, info.Trees, info.ReadMe)
			if err != nil {
				slog.Error("query llm", "err", err)
				return
			}
			mu.Lock()
			res[fullName] = answer
			mu.Unlock()
		}()*/
	}
	wg.Wait()

	for fullName, s := range res {
		fmt.Println("---------------------------------------------------------------------------------------------")
		fmt.Println(fullName, s)
	}
}
