package test

import (
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"poc-finder/model"
	"poc-finder/pkg/results"
)

type ModelTestSuite struct {
	suite.Suite
	DB   *gorm.DB
	mock sqlmock.Sqlmock
}

func (s *ModelTestSuite) SetupSuite() {
	db, mock, err := sqlmock.New()
	s.Require().NoError(err)

	gdb, err := gorm.Open(postgres.New(postgres.Config{
		Conn:                 db,
		PreferSimpleProtocol: true,
	}), &gorm.Config{})
	s.Require().NoError(err)

	s.DB = gdb
	s.mock = mock
}

func (s *ModelTestSuite) TearDownSuite() {
	sqlDB, err := s.DB.DB()
	s.Require().NoError(err)

	// 让 mock 期待接下来会发生一次 Close()
	s.mock.ExpectClose()

	// 真正调用 Close()
	err = sqlDB.Close()
	s.Require().NoError(err)

	// 最后再验一次 expectation
	s.Require().NoError(s.mock.ExpectationsWereMet())
}

func (s *ModelTestSuite) TestSaveRepoRecord_Insert() {
	now := time.Now()
	repo := &results.RepoInfo{
		VulnName:       "cve-2025-18002",
		RepositoryName: "user/repo",
		SourceType:     "github",
		Description:    "desc",
		HasPOC:         true,
		FileName:       "p.c",
		FileLink:       "http://",
		Reason:         "r",
	}

	// 准备一个空的 Vulnerability，调用后它的 Records 应该被 append
	vuln := &model.Vulnerability{}

	// 1. Expect Begin (GORM 会在 upsert 时启用事务)
	s.mock.ExpectBegin()

	// 2. Expect Exec — 匹配包含关键字即可
	s.mock.
		ExpectExec(`INSERT INTO "repo_records".*ON CONFLICT.*DO UPDATE.*`).
		WithArgs(
			sqlmock.AnyArg(), // uuid
			repo.VulnName,
			repo.RepositoryName,
			repo.SourceType,
			repo.Description,
			repo.HasPOC,
			repo.FileName,
			repo.FileLink,
			repo.Reason,
			now, // FirstChecked
			now, // LastChecked
		).
		WillReturnResult(sqlmock.NewResult(1, 1))

	// 3. Expect Commit (如果 GORM 自动 Commit 而不是 Rollback)
	//    或者 ExpectRollback，具体看 GORM 版本会用哪一个
	s.mock.ExpectCommit()

	// 4. 调用函数
	err := model.SaveRepoRecord(s.DB, vuln, repo, now)
	s.Require().NoError(err)

	// 5. 验证所有 expectation 都被触发
	s.Require().NoError(s.mock.ExpectationsWereMet())
}

func (s *ModelTestSuite) TestSearchVulnerability() {
	// 1) 预期主表查询
	rows := sqlmock.NewRows([]string{
		"vul_id", "vul_name", "description", "tag", "has_poc_or_exp",
		"closure_time", "created_at", "updated_at", "next_retry",
	}).AddRow("V1", "N1", "D", "T", false,
		time.Now(), time.Now(), time.Now(), time.Now(),
	)
	s.mock.
		ExpectQuery(`SELECT .*FROM "vulnerabilities" LEFT JOIN repo_records`).
		WithArgs("V1").
		WillReturnRows(rows)

	// 2) 预期 Preload 的子表查询：repo_records
	//    返回空就行，表示该漏洞目前没有任何 Record
	s.mock.
		ExpectQuery(`SELECT \* FROM "repo_records" WHERE "repo_records"\."vul_id" = \$1`).
		WithArgs("V1").
		WillReturnRows(sqlmock.NewRows([]string{
			"id", "vul_id", "repository", "source_type", "description",
			"has_poc", "poc_file", "poc_link", "reason", "first_checked", "last_checked",
		}))

	// 调用实际代码
	res, err := model.SearchVulnerability(s.DB, "V1", "", "", "")
	s.Require().NoError(err)
	s.Len(res, 1)

	// 验证所有期望都被满足
	s.Require().NoError(s.mock.ExpectationsWereMet())
}

func TestModelTestSuite(t *testing.T) {
	suite.Run(t, new(ModelTestSuite))
}
