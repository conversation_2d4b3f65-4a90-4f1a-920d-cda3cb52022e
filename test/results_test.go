package test

import (
	"log/slog"
	"os"
	"strings"
	"testing"
	"time"

	"poc-finder/config"
	"poc-finder/model"
)

var content = `LoRexxar/LoRexxar Here's the output:

1. Conclusion:
- No

2. POC/EXP File Name (POC/EXP Code):
-

3. Links Found:
-

4. Brief Judgment Reason:
There is no folder called "FineCMS" in the repository directory, and there is no example description of exploiting CVE-2017-11586 in the README. The file contents are not provided for analysis. However, there is a list of CVEs that LoRexxar has patched, including CVE-2017-11586, but it's mentioned as one of the vulnerabilities they've fixed, not exploited.`

func Test_ResultHandle(t *testing.T) {
	repo := repoInfo{
		Answer: content,
	}
	if len(repo.Answer) == 0 {
		slog.Error("LLM的回答是空的")
		return
	}
	// todo: 后续研究结构化格式输出或类似jinja编译模板形式的prompt
	answer := repo.Answer
	ss := strings.Split(answer, "\n")
	idxMap := make(map[string][]int, 4)
	for i, line := range ss {
		if len(line) == 0 {
			continue
		}
		if strings.Contains(line, "Conclusion:") {
			idxMap["conclusion"] = append(idxMap["conclusion"], i)
			continue
		}
		if strings.Contains(line, "POC/EXP File Name") {
			idxMap["conclusion"] = append(idxMap["conclusion"], i-1)
			idxMap["filename"] = append(idxMap["filename"], i)
			continue
		}
		if strings.Contains(line, "Links Found:") {
			idxMap["filename"] = append(idxMap["filename"], i-1)
			idxMap["link"] = append(idxMap["link"], i)
			continue
		}
		if strings.Contains(line, "Brief Judgment Reason:") {
			idxMap["link"] = append(idxMap["link"], i-1)
			idxMap["reason"] = append(idxMap["reason"], i)
			continue
		}
	}

	cs := idxMap["conclusion"]
	if len(cs) != 2 {
		t.Error("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
		return
	}
	slice := ss[cs[0]:cs[1]]
	for _, s := range slice {
		ls := strings.ToLower(s)
		if strings.Contains(ls, "no") {
			repo.HasPOC = false
			break
		} else if strings.Contains(ls, "yes") {
			repo.HasPOC = true
			break
		}
	}

	fs := idxMap["filename"]
	if len(fs) != 2 {
		t.Error("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
		return
	}
	slice = ss[fs[0]:fs[1]]
	for _, s := range slice {
		if !repo.HasPOC {
			break
		}
		s = strings.ReplaceAll(s, "**", "")
		s = strings.ReplaceAll(s, "2. POC/EXP File Name (POC/EXP Code):", "")
		s = strings.Replace(s, "-", "", 1)
		s = strings.TrimSpace(s)
		if len(s) > 0 {
			repo.FileName = s
			break
		}
	}

	ls := idxMap["link"]
	if len(ls) != 2 {
		t.Error("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
		return
	}
	slice = ss[ls[0]:ls[1]]
	for _, s := range slice {
		if !repo.HasPOC {
			break
		}
		s = strings.ReplaceAll(s, "**", "")
		s = strings.ReplaceAll(s, "3. Links Found:", "")
		s = strings.Replace(s, "-", "", 1)
		s = strings.TrimSpace(s)
		if len(s) > 0 {
			repo.FileLink = s
			break
		}
	}

	rs := idxMap["reason"]
	if len(rs) != 1 {
		t.Error("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
		return
	}
	slice = ss[rs[0]:]
	s := strings.Join(slice, "\n")
	s = strings.ReplaceAll(s, "**", "")
	s = strings.ReplaceAll(s, "4. Brief Judgment Reason:", "")
	s = strings.Replace(s, "-", "", 1)
	s = strings.TrimSpace(s)
	repo.Reason = s
}

func Test_DingDingWebHook(t *testing.T) {
	//accessToken := "9e9988a0950e87d82d721b028bd591b46f1c523a9f8dcb4e0867c0f5fd99ac82"
	//secret := "SECdce4038b82e06dcb1bc532c678b2f0fb13a1a26680bcff40b8408195dcb122cc"
	// 示例数据
	vuln := model.Vulnerability{
		VulnID:      "VUL-2025-001",
		VulnName:    "Example Overflow",
		Description: "在 XXX 模块里存在一个缓冲区溢出漏洞。",
		Tag:         "buffer-overflow",
		ClosureTime: time.Date(2025, 6, 15, 0, 0, 0, 0, time.UTC),
		Records: []model.Record{
			{RepoName: "nuclei-templates", RepoLink: "https://github.com/projectdiscovery/nuclei-templates", Description: "GitHub", POCFile: "exploit_A.go", POCLink: "https://git.io/exploitA", SourceType: "Nuclei"},
			{RepoName: "PoC-in-GitHub", RepoLink: "https://github.com/nomi-sec/PoC-in-GitHub", Description: "📡 PoC auto collect from GitHub. ⚠️ Be careful Malware.", POCFile: "proof_B.py", POCLink: "https://git.internal/proofB", SourceType: "Github"},
		},
	}

	data, err := os.ReadFile("./cmd/config.toml")
	if err != nil {
		t.Error(err)
	}
	cfg := config.NewConfig()
	if err = cfg.Load(string(data)); err != nil {
		t.Error(err)
	}
	if err = cfg.Notifier.NotifyVulnerability(&vuln); err != nil {
		t.Error(err)
	}
}
