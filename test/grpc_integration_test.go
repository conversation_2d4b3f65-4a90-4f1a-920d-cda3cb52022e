package test

import (
	"context"
	"fmt"
	"testing"
	"time"

	grpcpkg "poc-finder/pkg/grpc"
	pb "poc-finder/proto"
)

const (
	grpcAddress = "localhost:50051"
	testTimeout = 10 * time.Second
)

// setupRealClient 连接到真实的gRPC服务器
func setupRealClient(t *testing.T) (pb.VulnPusherClient, func()) {
	// 使用新的辅助函数连接到真实的gRPC服务器
	opts := grpcpkg.DefaultClientOptions(grpcAddress)

	conn, err := grpcpkg.NewClientConnection(opts)
	if err != nil {
		t.Skipf("无法连接到gRPC服务器 %s: %v (请先启动服务器)", grpcAddress, err)
	}

	client := pb.NewVulnPusherClient(conn)

	cleanup := func() {
		conn.Close()
	}

	return client, cleanup
}

// TestRealServer_PushInitial 测试真实服务器的初始化推送
func TestRealServer_PushInitial(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), testTimeout)
	defer cancel()

	req := &pb.PushInitialRequest{
		InitialMessage: &pb.InitialMessage{
			Version:   "1.0.0",
			VulnCount: 10,
			Interval:  "1h",
			Provider: []*pb.Provider{
				{
					Name:        "test_provider",
					DisplayName: "Test Provider",
					Link:        "https://test.com",
				},
			},
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushInitial(ctx, req)
	if err != nil {
		t.Fatalf("PushInitial 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushInitial 成功: %s", resp.Message)
}

// TestRealServer_PushVuln_Normal 测试真实服务器的普通漏洞推送
func TestRealServer_PushVuln_Normal(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), testTimeout)
	defer cancel()

	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			UniqueKey:    "integration-test-001",
			Title:        "Integration Test Vulnerability",
			Description:  "This is a test vulnerability for integration testing",
			Severity:     "High",
			Cve:          "CVE-2024-9001",
			Disclosure:   "2024-01-01",
			Solutions:    "Update to latest version",
			GithubSearch: []string{"CVE-2024-9001", "integration test"},
			References:   []string{"https://example.com/vuln"},
			Tags:         []string{"integration", "test"}, // 普通标签
			From:         "integration_test",
			Reason:       []string{"automated_testing"},
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushVuln (普通漏洞) 成功: %s", resp.Message)
}

// TestRealServer_PushVuln_NucleiTemplate 测试真实服务器的Nuclei模板漏洞推送
func TestRealServer_PushVuln_NucleiTemplate(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), testTimeout)
	defer cancel()

	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			UniqueKey:   "nuclei-integration-test-001",
			Title:       "Nuclei Template Integration Test",
			Description: "This vulnerability should only trigger Nuclei handler",
			Severity:    "Medium",
			Cve:         "CVE-2024-9002",
			Tags:        []string{"nuclei-templates", "integration", "test"}, // 包含nuclei-templates标签
			From:        "nuclei_integration_test",
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushVuln (Nuclei模板) 成功: %s", resp.Message)
}

// TestRealServer_PushText 测试真实服务器的文本推送
func TestRealServer_PushText(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), testTimeout)
	defer cancel()

	req := &pb.PushTextRequest{
		Message:   "Integration test message from automated testing",
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushText(ctx, req)
	if err != nil {
		t.Fatalf("PushText 调用失败: %v", err)
	}

	if !resp.Success {
		t.Errorf("期望 success=true, 实际得到 success=%v, message=%s", resp.Success, resp.Message)
	}

	t.Logf("PushText 成功: %s", resp.Message)
}

// TestRealServer_PushVuln_ErrorHandling 测试真实服务器的错误处理
func TestRealServer_PushVuln_ErrorHandling(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), testTimeout)
	defer cancel()

	// 测试缺少标识符的漏洞
	req := &pb.PushVulnRequest{
		VulnInfo: &pb.VulnInfo{
			// 缺少 UniqueKey, Title, CVE 等关键标识符
			Description: "Vulnerability without identifiers for error testing",
			Severity:    "Low",
		},
		Timestamp: time.Now().Unix(),
	}

	resp, err := client.PushVuln(ctx, req)
	if err != nil {
		t.Fatalf("PushVuln 调用失败: %v", err)
	}

	// 应该返回失败
	if resp.Success {
		t.Errorf("期望 success=false (因为缺少标识符), 实际得到 success=%v", resp.Success)
	}

	t.Logf("错误处理测试成功: %s", resp.Message)
}

// TestRealServer_Batch 测试批量推送
func TestRealServer_Batch(t *testing.T) {
	client, cleanup := setupRealClient(t)
	defer cleanup()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 发送5个漏洞
	for i := 1; i <= 5; i++ {
		req := &pb.PushVulnRequest{
			VulnInfo: &pb.VulnInfo{
				UniqueKey: fmt.Sprintf("batch-test-%d", i),
				Title:     fmt.Sprintf("Batch Test Vulnerability %d", i),
				Cve:       fmt.Sprintf("CVE-2024-900%d", i),
				Severity:  "Medium",
				Tags:      []string{"batch", "test"},
				From:      "batch_test",
			},
			Timestamp: time.Now().Unix(),
		}

		resp, err := client.PushVuln(ctx, req)
		if err != nil {
			t.Fatalf("批量测试第%d个请求失败: %v", i, err)
		}

		if !resp.Success {
			t.Errorf("批量测试第%d个请求返回失败: %s", i, resp.Message)
		}

		t.Logf("批量测试 %d/5 成功", i)

		// 间隔一秒避免过快请求
		time.Sleep(1 * time.Second)
	}

	t.Logf("批量测试完成")
}
