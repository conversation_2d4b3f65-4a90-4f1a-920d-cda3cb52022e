package test

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"testing"

	"github.com/ollama/ollama/api"
	"github.com/revrost/go-openrouter"

	"poc-finder/pkg/llm"
)

var linkQuery = fmt.Sprintf(`
You are a professional cybersecurity analyst.

Your task:
Given the name of a target vulnerability and the link to a GitHub repository, determine whether this repository contains a proof-of-concept (POC) or exploit (EXP) for the specified vulnerability.

Target Vulnerability Name: %s  
GitHub Repository Link: %s 
GitHub Repository File list information: %s

Instructions:
- Carefully analyze the repository's structure, including file and folder names, to identify possible POCs or EXPs.
- Focus on the presence of keywords like "poc", "exploit", or the CVE ID itself in file names or directory names.
- Pay special attention to README content or code usage examples.
- Avoid false positives: exclude content related to dashboards or admin panels (keywords like "dashboard", "panel").
- The most likely POC/EXP often:
  - Appears in a file or folder named after the product or CVE
  - Contains executable or demonstration code
  - Is mentioned in README instructions (e.g., “python exploit.py”)

——————————————————————————————  
Output your answer strictly in the following format:

1. Conclusion:
- "Yes" or "No"

2. POC/EXP File Name (POC/EXP Code):
- If your conclusion is "Yes", provide the most likely file name(s) that match the vulnerability

3. Links Found:
- If you can construct the direct GitHub link to the file(s), provide it here. Examples:
    "https://github.com/username/repo_name/blob/main/exploit.py"
    "https://github.com/username/repo_name/blob/main/poc/example.c"

4. Brief Judgment Reason:
- Explain your reasoning using observed evidence, such as:
  - Folder or file names contain "poc", "exploit", or the CVE ID
  - README includes execution or compile instructions
  - The structure or naming aligns with typical POC/EXP repositories

——————————————————————————————  
Please follow the format and be thorough in your analysis.
`, "CVE-2024-15230", "https://github.com/projectdiscovery/nuclei-templates",
	"https://api.github.com/repos/projectdiscovery/nuclei-templates/git/trees/main?recursive=1")

func Test_OllamaQuery(t *testing.T) {
	if err := os.Setenv("OLLAMA_HOST", "***********"); err != nil {
		t.Fatal(err)
	}

	client, err := api.ClientFromEnvironment()
	if err != nil {
		t.Fatal(err)
	}
	model := "gemma3:4b-it-qat"
	prompt := "帮我确认以下内容是否指代苹果\n为落叶乔木，在世界上广泛种植。果实又称柰或林檎，一般呈红色，但需视品种而定，富含矿物质和维生素，是人们最常食用的水果之一"
	messages := []api.Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	req := &api.ChatRequest{
		Model:    model,
		Messages: messages,
	}

	respFunc := func(response api.ChatResponse) error {
		fmt.Print(response.Message.Content)
		return nil
	}

	if err = client.Chat(context.Background(), req, respFunc); err != nil {
		t.Fatal(err)
	}
}

func Query(llmType, model, CVEID, UserName, RepoName, Description, Trees, ReadMe string) (string, error) {

	if err := os.Setenv("OLLAMA_HOST", "************"); err != nil {
		return "", err
	}

	client, err := api.ClientFromEnvironment()
	if err != nil {
		return "", err
	}
	question := fmt.Sprintf(`
You are a cybersecurity analyst.
Target Vulnerability Name: %s
Given the following GitHub repository information, determine whether it contains a proof-of-concept (POC) or exploit (EXP) for the above target vulnerability name.
Select one of the files that most likely refers to the vulnerability.
Exclude keywords dashboard, panel.
Tips:
It is necessary to pay attention to the relationship between folders and file paths in the Directory structure
- The folder name of a possible POC or EXP is generally the name of the product affected by the vulnerability
- If the input ID keyword is included in the single file name, it is likely to be the target POC or EXP
——————
[Basic repository information]
User Name: %s
Repository Name: %s
Repository Description: %s
——————
[Directory structure (path, url, size are listed. A size of 0 indicates a folder)]
%s
——————
【README.md content】
%s`, CVEID, UserName, RepoName, Description, Trees, ReadMe)
	expectedFormat := fmt.Sprintf(`
——————
Based on the above information, please output the conclusion in the following format:

1. Conclusion:
- "Yes" or "No"

2. POC/EXP File Name (POC/EXP Code):
- If the conclusion is "yes", give the most likely file name (can be one or two lines of examples)

3. Links Found:
- If you can piece together the full URL of the file on GitHub, please give it. If no links found, keep it blank. For example:
	"https://github.com/username/warehouse_name/blob/main/exploit.py"
	"https://github.com/username/warehouse_name/blob/main/poc/example.c"

4. Brief Judgment Reason:
- Combine the keywords that appear in the warehouse name, directory, README (such as "poc", "exploit", "cve", compilation/execution examples) to explain your judgment.
- For example: "There is a folder called 'poc' in the repository directory, there is an example description of 'Usage: python exploit.py' in the README, and the file name contains 'exploit', so it is highly suspected to be POC/EXP."

——————————————————————————————
Please answer completely according to the above format.`)
	prompt := strings.Builder{}
	prompt.WriteString(question)
	prompt.WriteString("\n")
	prompt.WriteString(expectedFormat)
	if llmType == "openrouter" {
		return "", QueryOpenRouter(prompt.String(), model)
	}
	messages := []api.Message{
		{
			Role:    "user",
			Content: prompt.String(),
		},
	}

	req := &api.ChatRequest{
		Model:    model,
		Messages: messages,
	}

	var sb strings.Builder
	respFunc := func(response api.ChatResponse) error {
		sb.WriteString(response.Message.Content)
		return nil
	}

	if err = client.Chat(context.Background(), req, respFunc); err != nil {
		return "", err
	}
	return sb.String(), nil
}

func QueryOpenRouter(query, model string) error {
	apiKey := os.Getenv("OPEN_ROUTER_API_KEY")
	if apiKey == "" {
		return fmt.Errorf("请设置环境变量 OPENROUTER_API_KEY")
	}

	client := openrouter.NewClient(
		apiKey,
		openrouter.WithXTitle("Vulnerability Finding"),
		openrouter.WithHTTPReferer("https://openrouter.ai/api/v1"),
	)
	resp, err := client.CreateChatCompletion(
		context.Background(),
		openrouter.ChatCompletionRequest{
			Model: model,
			Messages: []openrouter.ChatCompletionMessage{
				{
					Role:    openrouter.ChatMessageRoleUser,
					Content: openrouter.Content{Text: query},
				},
			},
			//ResponseFormat: &openrouter.ChatCompletionResponseFormat{
			//	Type: openrouter.ChatCompletionResponseFormatTypeJSONSchema,
			//	JSONSchema: &openrouter.ChatCompletionResponseFormatJSONSchema{
			//		Name:   "poc finder",
			//		Schema: o.schema(),
			//		Strict: true,
			//	},
			//},
		},
	)

	if err != nil {
		return err
	}

	fmt.Println(resp.Choices[0].Message.Content)
	return nil
}

func Test_OpenRouterLink(t *testing.T) {
	err := QueryOpenRouter(linkQuery, openrouter.GPT4o)
	if err != nil {
		t.Fatal(err)
	}
}

func Test_OpenRouterFormat(t *testing.T) {
	apiKey := os.Getenv("OPEN_ROUTER_API_KEY")
	if apiKey == "" {
		t.Error("请设置环境变量 OPENROUTER_API_KEY")
		return
	}

	client := openrouter.NewClient(
		apiKey,
		openrouter.WithXTitle("Vulnerability Finding"),
		openrouter.WithHTTPReferer("https://openrouter.ai/api/v1"),
	)

	var j llm.JSONFormat
	if err := json.Unmarshal([]byte(llm.SchemaJSON), &j); err != nil {
		t.Error(err)
	}

	resp, err := client.CreateChatCompletion(
		context.Background(),
		openrouter.ChatCompletionRequest{
			Model: openrouter.GPT4oMini,
			Messages: []openrouter.ChatCompletionMessage{
				{
					Role:    openrouter.ChatMessageRoleUser,
					Content: openrouter.Content{Text: "Check if the schema is correct."},
				},
			},
			ResponseFormat: &openrouter.ChatCompletionResponseFormat{
				Type: openrouter.ChatCompletionResponseFormatTypeJSONSchema,
				JSONSchema: &openrouter.ChatCompletionResponseFormatJSONSchema{
					Name:   "poc finder",
					Schema: &j,
					Strict: true,
				},
			},
		},
	)

	if err != nil {
		t.Error(err)
	}

	fmt.Println(resp.Choices[0].Message.Content)
}

func Test_OpenRouterDebug(t *testing.T) {
	apiKey := os.Getenv("OPEN_ROUTER_API_KEY")
	if apiKey == "" {
		t.Error("请设置环境变量 OPENROUTER_API_KEY")
		return
	}

	proxyURL, _ := url.Parse("http://127.0.0.1:8080")
	client := &http.Client{
		Transport: &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	body := `{
    "model": "openai/gpt-4",
    "messages": [
      {"role": "user", "content": "What is the weather like in London?"},
    ],
  }`
	req, err := http.NewRequestWithContext(context.Background(), http.MethodPost, "https://openrouter.ai/api/v1/chat/completions", strings.NewReader(body))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")
	resp, err := client.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(string(data))
}
