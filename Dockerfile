# --- Build Stage ---
# 使用官方的 Go 镜像作为构建环境
# ┌───────────── 第一阶段：编译
#FROM golang:1.24.1-alpine AS builder
#ENV CGO_ENABLED=0
#WORKDIR /src
#
## 1. 复制 go.mod / go.sum，提前下载依赖
#COPY go.mod go.sum ./
#RUN go mod download
#
## 2. 复制全部源码并编译
#COPY . .
## 假设主包入口是 cmd/main.go
#RUN go build -o poc-finder ./cmd/.

# ────────────── 第二阶段：运行时镜像
FROM alpine:3
RUN apk add --no-cache ca-certificates \
 && apk add --update tzdata \
 && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
 && echo "Asia/Shanghai" > /etc/timezone \
 && apk del tzdata \
 && rm -rf /var/cache/apk/*

# 拷贝编译好的二进制文件
WORKDIR /app
#COPY --from=builder /app/poc_finder /usr/bin/poc_finder
ENTRYPOINT ["/app/poc-finder"]