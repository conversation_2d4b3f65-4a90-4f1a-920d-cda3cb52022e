package handler

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/semaphore"
	"golang.org/x/time/rate"
	"gorm.io/gorm"

	"poc-finder/config"

	"poc-finder/model"
	"poc-finder/pkg/llm"
	"poc-finder/pkg/results"
)

// todo: 预留 cve 数据源的处理

type CVEHandler struct {
	apiURL  string
	vulID   string
	results map[string]*results.RepoInfo
	vuln    *model.Vulnerability

	db     *gorm.DB
	client *resty.Client
	resp   *resty.Response
	llm    config.LLM

	ctx           context.Context
	dataSemaphore *semaphore.Weighted
	llmSemaphore  *semaphore.Weighted
	nextRetry     time.Duration
	limiter       *rate.Limiter
}

func NewCVE(cfg *config.Config) DataSource {
	targetAPI := "https://api.github.com/search/repositories?q={vuln}+in:name,description,readme&sort=created&order=desc&per_page=10"
	client := cfg.CVEClient
	client.SetAuthScheme("Bearer").SetAuthToken(cfg.Source.GitHubBearer)
	client.SetHeader("Accept", "application/vnd.github.v3+json")
	client.SetHeader("Accept-Charset", "utf-8")
	client.SetBaseURL(targetAPI)
	// 限制到每秒 2 次请求，burst 允许瞬间最高 10 个令牌：瞬间最多排队 10 个请求
	limiter := rate.NewLimiter(rate.Every(time.Second/time.Duration(cfg.Source.Nuclei.LimitNum)), 10)

	return &CVEHandler{
		client:        client,
		db:            cfg.DB,
		results:       make(map[string]*results.RepoInfo),
		ctx:           cfg.Context(),
		nextRetry:     24 * cfg.Source.NextRetry * time.Hour,
		dataSemaphore: semaphore.NewWeighted(int64(cfg.Source.CVE.Concurrency)),
		llmSemaphore:  semaphore.NewWeighted(int64(cfg.Model.Concurrency)),
		vuln:          &model.Vulnerability{},
		limiter:       limiter,
	}
}

func (c *CVEHandler) Request(task VulnTask) error {
	var vulnID, vulnName string

	// 根据任务类型确定搜索值
	switch task.Type {
	case "ID":
		vulnID = task.Value
	case "Name":
		vulnName = task.Value
	default:
		vulnID, vulnName = task.Value, task.Value
	}

	var vuln string
	if len(vulnName) != 0 {
		vuln = vulnName
		c.vuln.VulnName = vulnName
	} else if len(vulnID) != 0 {
		vuln = vulnID
		c.vuln.VulnID = vulnID
	}
	c.vuln.Description = task.Description
	c.vuln.Tag = strings.Join(task.Tags, ",")
	disclosure, _ := time.Parse("2006-01-02", task.Disclosure)
	c.vuln.ClosureTime = disclosure

	c.client.SetPathParam("vuln", vuln)
	// 如果不能及时取到令牌，就会阻塞直到可以
	if err := c.limiter.Wait(c.ctx); err != nil {
		return err
	}
	resp, err := c.client.R().Get(c.client.BaseURL)
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 {
		return fmt.Errorf("request vul error, name:%s, http status code:%s", vulnID, resp.Status())
	}

	c.resp = resp
	return nil
}

func (c *CVEHandler) Handle() error {
	dataSource := new(model.GithubData)
	if err := c.client.JSONUnmarshal(c.resp.Body(), dataSource); err != nil {
		return err
	}

	var (
		wg sync.WaitGroup
		mu sync.Mutex
	)
	for _, item := range dataSource.Items {
		wg.Add(1)
		_ = c.dataSemaphore.Acquire(c.ctx, 1)
		go func(item model.Item) {
			defer wg.Done()
			defer c.dataSemaphore.Release(1)
			fullName := item.FullName
			mu.Lock()
			c.results[fullName] = &results.RepoInfo{VulnName: c.vulID, Description: item.Description}
			ss := strings.Split(fullName, "/")
			if len(ss) != 2 {
				slog.ErrorContext(c.ctx, "repo split", slog.String("invalid repo name", fullName))
				c.results[fullName].UserName = fullName
				c.results[fullName].RepositoryName = fullName
			} else {
				c.results[fullName].UserName = ss[0]
				c.results[fullName].RepositoryName = ss[1]
			}
			mu.Unlock()
		}(item)

	}
	wg.Wait()

	return c.QueryLLM()
}

func (c *CVEHandler) QueryLLM() error {
	var (
		wg sync.WaitGroup
		mu sync.Mutex
	)
	for fullName, info := range c.results {
		wg.Add(1)
		_ = c.llmSemaphore.Acquire(c.ctx, 1)
		go func() {
			defer wg.Done()
			defer c.llmSemaphore.Release(1)
			prompt, err := llm.Prompt(info)
			if err != nil {
				slog.Error("llm prompt failed", slog.String("full.name", fullName), slog.String("vul.id", c.vulID), slog.String("err", err.Error()))
				return
			}
			answer, err := c.llm.Query(c.ctx, prompt.String())
			if err != nil {
				slog.Error("query failed", slog.String("full.name", fullName), slog.String("vul.id", c.vulID), slog.String("err", err.Error()))
				return
			}
			info.Answer = answer
			mu.Lock()
			c.results[fullName] = info
			mu.Unlock()
		}()

	}
	return nil
}

func (c *CVEHandler) Results() (*model.Vulnerability, error) {
	for _, repo := range c.results {
		if err := repo.Handle(); err != nil {
			slog.ErrorContext(c.ctx, "处理响应", "err", err, "repo.name", repo.RepositoryName)
			continue
		}

		if repo.HasPOC {
			c.vuln.HasPOCOrEXP = true
		}

		if _, err := model.SaveOrUpdateVuln(c.db, c.vuln, repo, c.nextRetry, model.SaveRepoRecord); err != nil {
			slog.ErrorContext(c.ctx, "save repo record", slog.String("err", err.Error()))
			return nil, err
		}
	}
	return nil, nil
}

func (c *CVEHandler) GetName() string {
	return CVE
}
