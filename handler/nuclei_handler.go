package handler

import (
	"context"
	"fmt"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/semaphore"
	"gorm.io/gorm"

	"poc-finder/config"
	"poc-finder/model"
	"poc-finder/pkg/results"
)

type NucleiHandler struct {
	db     *gorm.DB
	client *resty.Client
	resp   *resty.Response
	result *results.RepoInfo
	vuln   *model.Vulnerability

	sema      *semaphore.Weighted
	ctx       context.Context
	nextRetry time.Duration
}

func NewNuclei(cfg *config.Config) DataSource {
	targetURL := "https://api.github.com/search/code?q={vuln}+repo:projectdiscovery/nuclei-templates"
	client := cfg.NucleiClient
	client.SetHeader("Accept", "application/vnd.github.v3+json")
	client.SetHeader("Accept-Charset", "utf-8")
	client.SetAuthScheme("Bearer").SetAuthToken(cfg.Source.GitHubBearer)
	client.SetBaseURL(targetURL)

	return &NucleiHandler{
		db:        cfg.DB,
		client:    client,
		ctx:       cfg.Context(),
		nextRetry: 24 * cfg.Source.NextRetry * time.Hour,
		sema:      semaphore.NewWeighted(int64(cfg.Source.Nuclei.Concurrency)),
		vuln:      &model.Vulnerability{},
	}
}

func (n *NucleiHandler) Request(task VulnTask) error {
	var vulnID, vulnName string

	// 根据任务类型确定搜索值
	switch task.Type {
	case "ID":
		vulnID = task.Value
	case "Name":
		vulnName = task.Value
	default:
		vulnID, vulnName = task.Value, task.Value
	}

	var vuln string
	if len(vulnName) != 0 {
		vuln = vulnName
		vuln = strings.Replace(vuln, " -", "", -1)
		vuln = url.QueryEscape(vuln)
	} else if len(vulnID) != 0 {
		vuln = vulnID
	}
	n.vuln.VulnName = vulnName
	n.vuln.VulnID = vulnID
	n.vuln.Description = task.Description
	n.vuln.Tag = strings.Join(task.Tags, ",")
	disclosure, _ := time.Parse("2006-01-02", task.Disclosure)
	n.vuln.ClosureTime = disclosure
	n.result = &results.RepoInfo{
		VulnName:   vuln,
		SourceType: Nuclei,
	}
	n.client.SetPathParam("vuln", vuln)
	resp, err := n.client.R().Get(n.client.BaseURL)
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 {
		return fmt.Errorf("request vul error, name:%s, http status code:%s", vulnID, resp.Status())
	}
	n.resp = resp
	return nil
}

func (n *NucleiHandler) Handle() error {
	dataSource := new(model.SourceData)
	if err := n.client.JSONUnmarshal(n.resp.Body(), dataSource); err != nil {
		return err
	}
	for _, item := range dataSource.Items {
		if filepath.Ext(item.Name) == ".yaml" {
			n.result.HasPOC = true
			n.result.FileName = item.Name
			n.result.FileLink = item.HTMLURL
			n.result.UserName = "projectdiscovery"
			n.result.RepositoryName = "nuclei-templates"
			n.result.RepoLink = item.HTMLURL
			n.result.Description = item.Repository.Description
			// todo: 仅保留第一个yaml结果，需要研究这样做是否正确
			n.vuln.HasPOCOrEXP = true
			break
		}
	}

	// 不论是否搜索到，都存入Vulnerability表中
	if _, err := model.SaveOrUpdateVuln(n.db, n.vuln, n.result, n.nextRetry, model.SaveRepoRecord); err != nil {
		return err
	}

	return nil
}

func (n *NucleiHandler) Results() (*model.Vulnerability, error) {
	return n.vuln, nil
}

func (n *NucleiHandler) QueryLLM() error {
	return nil
}

func (n *NucleiHandler) GetName() string {
	return Nuclei
}
