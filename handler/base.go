package handler

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"poc-finder/config"
	"poc-finder/model"
)

const (
	Github = "Github"
	Nuclei = "Nuclei"
	CVE    = "CVE"
)

type DataSource interface {
	Request(task VulnTask) error
	Handle() error
	QueryLLM() error
	GetName() string
	Results() (*model.Vulnerability, error)
}

// 注册表：映射源名称到构造函数
var registry = map[string]func(*config.Config) DataSource{}

func init() {
	// 程序启动时自动注册
	Register(Github, NewGithub)
	Register(Nuclei, NewNuclei)
	Register(CVE, NewCVE)
}

// Register 用于在 init() 中注册具体数据源
func Register(name string, ctor func(*config.Config) DataSource) {
	registry[name] = ctor
}

// GetSources 根据配置启用情况，返回所有启用的数据源实例
func GetSources(cfg *config.Config) []DataSource {
	var list []DataSource

	// 按照在 registry 中注册的顺序依次取出
	// 也可以根据配置里的顺序动态决定
	for name, ctor := range registry {
		switch name {
		case Github:
			if cfg.Source.GitHub.Enabled {
				list = append(list, ctor(cfg))
			}
		case Nuclei:
			if cfg.Source.Nuclei.Enabled {
				list = append(list, ctor(cfg))
			}
		case CVE:
			if cfg.Source.CVE.Enabled {
				list = append(list, ctor(cfg))
			}
		}
	}
	return list
}

// processWithAllSources 使用增强的任务上下文处理所有数据源
func processWithAllSources(ctx context.Context, cfg *config.Config, task VulnTask, nucleiTicker *time.Ticker, githubTicker *time.Ticker) *model.Vulnerability {
	sources := GetSources(cfg)
	var (
		vulns = make([]*model.Vulnerability, 0)
		v     *model.Vulnerability
	)
	for _, ds := range sources {
		switch ds.GetName() {
		case Nuclei:
			<-nucleiTicker.C // —— 只等 Nuclei 用的计时器
		case Github:
			<-githubTicker.C // —— 只等 GitHub 用的计时器
		case CVE:
			// CVE 也想限速就配一个 ticker，否则直接跳过等待
		}

		// 使用增强的调度函数
		v = dispatch(ctx, ds, task)
		if v != nil {
			vulns = append(vulns, v)
		}
	}

	if len(vulns) == 0 {
		return nil
	}

	var (
		vuln    model.Vulnerability
		records []model.Record
	)

	vuln = *vulns[0]
	vuln.Records = nil
	for _, vs := range vulns {
		for _, repo := range vs.Records {
			if repo.HasPOC {
				records = append(records, repo)
			}
		}
	}
	vuln.Records = records
	return &vuln
}

// processWithNucleiOnly 只使用Nuclei处理器处理漏洞
func processWithNucleiOnly(ctx context.Context, cfg *config.Config, task VulnTask, nucleiTicker *time.Ticker) *model.Vulnerability {
	// 获取Nuclei数据源
	sources := GetSources(cfg)
	var nucleiSource DataSource

	for _, ds := range sources {
		if ds.GetName() == Nuclei {
			nucleiSource = ds
			break
		}
	}

	if nucleiSource == nil {
		slog.WarnContext(ctx, "Nuclei数据源未启用", "vuln", task.Value)
		return nil
	}

	// 等待限流器
	<-nucleiTicker.C

	// 使用调度函数
	return dispatch(ctx, nucleiSource, task)
}

// containsNucleiTemplatesTag 检查标签中是否包含nuclei-templates关键字
func containsNucleiTemplatesTag(tags []string) bool {
	for _, tag := range tags {
		if strings.Contains(strings.ToLower(tag), "nuclei-templates") {
			return true
		}
	}
	return false
}

// dispatch 使用增强的任务上下文进行调度
func dispatch(ctx context.Context, ds DataSource, task VulnTask) *model.Vulnerability {
	if err := ds.Request(task); err != nil {
		slog.ErrorContext(ctx, "Request 失败", "vul", task.Value, "src", ds.GetName(), "err", err)
		return nil
	}

	// 后续处理步骤保持一致
	if err := ds.Handle(); err != nil {
		slog.ErrorContext(ctx, "Handle 失败", "vul", task.Value, "src", ds.GetName(), "err", err)
		return nil
	}
	if err := ds.QueryLLM(); err != nil {
		slog.ErrorContext(ctx, "QueryLLM 失败", "vul", task.Value, "src", ds.GetName(), "err", err)
		return nil
	}
	v, err := ds.Results()
	if err != nil {
		slog.ErrorContext(ctx, "Results 失败", "vul", task.Value, "src", ds.GetName(), "err", err)
		return nil
	}
	return v
}

// VulnTask 统一的漏洞任务结构
type VulnTask struct {
	Value string   // 漏洞ID或名称
	Type  string   // "ID" 或 "Name"
	Tags  []string // 漏洞标签，用于决定使用哪些处理器

	// 增强字段：为处理器提供更丰富的上下文信息
	Severity     string   // 漏洞严重程度
	Disclosure   string   // 披露时间
	References   []string // 参考链接
	GithubSearch []string // GitHub搜索关键词
	Description  string   // 漏洞描述
	UniqueKey    string   // 唯一标识符
	From         string   // 数据来源
}

// ProcessVulnTask 统一的漏洞处理入口 - 支持标签过滤，使用全局限流器
func ProcessVulnTask(ctx context.Context, cfg *config.Config, task VulnTask) *model.Vulnerability {
	// 获取全局限流器
	nucleiTicker := cfg.GetNucleiTicker()
	githubTicker := cfg.GetGithubTicker()

	// 检查是否只使用Nuclei处理器
	if containsNucleiTemplatesTag(task.Tags) {
		slog.InfoContext(ctx, "🎯 检测到nuclei-templates标签，仅使用Nuclei处理器",
			"vuln", task.Value, "tags", task.Tags, "severity", task.Severity)
		return processWithNucleiOnly(ctx, cfg, task, nucleiTicker)
	}

	// 使用所有启用的处理器（增强版本）
	slog.InfoContext(ctx, "🔍 使用所有启用的处理器进行搜索",
		"vuln", task.Value, "type", task.Type, "tags", task.Tags, "severity", task.Severity)
	return processWithAllSources(ctx, cfg, task, nucleiTicker, githubTicker)
}

// ProcessVulnTasks 批量处理漏洞任务 - 使用全局限流器的统一调度入口
func ProcessVulnTasks(ctx context.Context, cfg *config.Config, tasks []VulnTask) []*model.Vulnerability {
	if len(tasks) == 0 {
		slog.InfoContext(ctx, "没有待处理的漏洞任务")
		return nil
	}

	slog.InfoContext(ctx, "🚀 开始批量处理漏洞任务", "count", len(tasks))

	var results []*model.Vulnerability

	for i, task := range tasks {
		slog.InfoContext(ctx, "📋 处理漏洞任务",
			"progress", fmt.Sprintf("%d/%d", i+1, len(tasks)),
			"vuln", task.Value,
			"type", task.Type)

		// 使用全局限流器处理任务
		vuln := ProcessVulnTask(ctx, cfg, task)
		if vuln != nil {
			results = append(results, vuln)

			// 如果找到POC，发送通知
			if vuln.HasPOCOrEXP {
				if err := cfg.Notifier.NotifyVulnerability(vuln); err != nil {
					slog.ErrorContext(ctx, "💬 POC通知发送失败", "error", err, "vuln", task.Value)
				} else {
					slog.InfoContext(ctx, "✅ POC发现并已通知", "vuln", task.Value)
				}
			}
		}
	}

	slog.InfoContext(ctx, "🎉 批量处理完成",
		"total_tasks", len(tasks),
		"successful_results", len(results))

	return results
}
