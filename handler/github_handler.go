package handler

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"golang.org/x/sync/semaphore"
	"golang.org/x/time/rate"
	"gorm.io/gorm"

	config2 "poc-finder/config"
	"poc-finder/model"
	"poc-finder/pkg/llm"
	"poc-finder/pkg/results"
)

type GithubHandler struct {
	db       *gorm.DB
	llm      config2.LLM
	client   *resty.Client
	notifier config2.Notifier
	response *resty.Response
	results  map[string]*results.RepoInfo

	sourceSemaphore *semaphore.Weighted
	llmSemaphore    *semaphore.Weighted
	ctx             context.Context
	rawURL          string
	apiURL          string
	vuln            *model.Vulnerability
	nextRetry       time.Duration
	limiter         *rate.Limiter
}

func NewGithub(cfg *config2.Config) DataSource {
	targetAPI := "https://api.github.com/search/repositories?q={Vuln}+in:name,description,readme&sort=created&order=desc&per_page=10"
	client := cfg.GithubClient
	client.SetHeader("Accept", "application/vnd.github.v3+json")
	client.SetHeader("Accept-Charset", "utf-8")
	client.SetAuthScheme("Bearer").SetAuthToken(cfg.Source.GitHubBearer)
	client.SetBaseURL(targetAPI)
	// 限制到每秒 2 次请求，burst 允许瞬间最高 10 个令牌：瞬间最多排队 10 个请求
	limiter := rate.NewLimiter(rate.Every(time.Second/time.Duration(cfg.Source.Nuclei.LimitNum)), 10)

	return &GithubHandler{
		db:      cfg.DB,
		llm:     cfg.LLM,
		client:  client,
		ctx:     cfg.Context(),
		limiter: limiter,
		// 用于保存数据集结果
		results:         make(map[string]*results.RepoInfo),
		nextRetry:       24 * cfg.Source.NextRetry * time.Hour,
		sourceSemaphore: semaphore.NewWeighted(int64(cfg.Source.GitHub.Concurrency)),
		llmSemaphore:    semaphore.NewWeighted(int64(cfg.Model.Concurrency)),
		rawURL:          "https://raw.githubusercontent.com/",
		apiURL:          "https://api.github.com/repos/",
		vuln:            &model.Vulnerability{},
	}
}

func (g *GithubHandler) Request(task VulnTask) error {
	var vulnID, vulnName string

	// 根据任务类型确定搜索值
	switch task.Type {
	case "ID":
		vulnID = task.Value
	case "Name":
		vulnName = task.Value
	default:
		vulnID, vulnName = task.Value, task.Value
	}

	g.vuln.VulnID = vulnID
	g.vuln.VulnName = vulnName
	g.vuln.Description = task.Description
	g.vuln.Tag = strings.Join(task.Tags, ",")
	disclosure, _ := time.Parse("2006-01-02", task.Disclosure)
	g.vuln.ClosureTime = disclosure

	// 利用增强的任务信息
	if task.Description != "" {
		g.vuln.Description = task.Description
	}
	if task.Severity != "" {
		g.vuln.Severity = task.Severity
	}

	searchQuery := g.GetVulnName()

	// 如果有GitHub搜索关键词，优先使用
	if len(task.GithubSearch) > 0 {
		// 使用提供的GitHub搜索关键词
		searchQuery = strings.Join(task.GithubSearch, " ")
	}

	g.client.SetPathParam("Vuln", searchQuery)

	// 如果不能及时取到令牌，就会阻塞直到可以
	if err := g.limiter.Wait(g.ctx); err != nil {
		return err
	}
	resp, err := g.client.R().Get(g.client.BaseURL)
	if err != nil {
		return err
	}
	if resp.StatusCode() != 200 {
		return fmt.Errorf("request vul error, name:%s, http status code:%s", vulnID, resp.Status())
	}
	g.response = resp
	return nil
}

func (g *GithubHandler) Handle() error {
	if g.response == nil {
		return fmt.Errorf("response is nil")
	}
	dataSource := new(model.GithubData)
	if err := g.client.JSONUnmarshal(g.response.Body(), dataSource); err != nil {
		return err
	}

	var (
		wg sync.WaitGroup
		mu sync.Mutex
	)

	for _, item := range dataSource.Items {
		// 获取分支、树、README 都在同一个 goroutine 内完成
		wg.Add(1)

		// 只有获取到信号量（即没超过 maxWorkers）才会启动下一个 goroutine
		if err := g.sourceSemaphore.Acquire(g.ctx, 1); err != nil {
			slog.Error("semaphore acquire failed", "err", err)
			wg.Done()
			continue
		}

		go func(item model.Item) {
			defer wg.Done()
			defer g.sourceSemaphore.Release(1)

			fullName := item.FullName
			// —— 第一步：获取仓库名、仓库描述、用户名 —— //
			mu.Lock()
			g.results[fullName] = &results.RepoInfo{VulnName: g.GetVulnName(), RepoLink: item.HTMLURL, Description: item.Description, SourceType: Github}
			ss := strings.Split(fullName, "/")
			if len(ss) != 2 {
				slog.ErrorContext(g.ctx, "repo split", slog.String("invalid repo name", fullName))
				g.results[fullName].UserName = fullName
				g.results[fullName].RepositoryName = fullName
			} else {
				g.results[fullName].UserName = ss[0]
				g.results[fullName].RepositoryName = ss[1]
			}
			mu.Unlock()
			// —— 第二步：获取 branches 列表 —— //
			branchesURL, found := strings.CutSuffix(item.BranchesURL, "{/branch}")
			if !found {
				slog.Error("didn't find branch URL", "full_name", fullName)
				return
			}

			branchResp, err := g.client.R().Get(branchesURL)
			if err != nil {
				slog.Error("get branch list failed", "full_name", fullName, "err", err)
				return
			}
			var branches model.Branches
			if err := g.client.JSONUnmarshal(branchResp.Body(), &branches); err != nil {
				slog.Error("unmarshal branches failed", "full_name", fullName, "err", err)
				return
			}

			// 默认用 main，若有分支则用第一个分支名称
			branchName := "main"
			if len(branches) > 0 {
				branchName = branches[0].Name
			}

			// —— 第三步：获取树 (trees) —— //
			treeURL := fmt.Sprintf("%s%s/git/trees/%s?recursive=1", g.apiURL, fullName, branchName)
			treeResp, err := g.client.R().Get(treeURL)
			var readmePath string
			if err != nil {
				slog.Error("request trees failed", "full_name", fullName, "err", err)
			} else {
				// 解析 JSON，寻找 README 路径（有些仓库 README 不在根目录，这里通过tree查找到 README 路径）
				var treesData model.Trees
				if err = g.client.JSONUnmarshal(treeResp.Body(), &treesData); err != nil {
					slog.Error("unmarshal trees JSON failed", "full_name", fullName, "err", err)
				} else {
					strBuilder := strings.Builder{}
					for _, entry := range treesData.Tree {
						info := fmt.Sprintf("Path: %s\nURL: %s\nSize:%d\n\n", entry.Path, entry.URL, entry.Size)
						strBuilder.WriteString(info)
						if strings.EqualFold(entry.Path, "README.md") {
							// 找到根目录下的 README.md
							readmePath = entry.Path
						}
					}
					fileInfo := strBuilder.String()
					mu.Lock()
					g.results[fullName].Trees = fileInfo
					mu.Unlock()
				}
			}

			// —— 第四步：获取 README.md —— //
			// 如果在上面找到了特定路径，这里也可以拼接自定义相对路径
			if len(readmePath) == 0 {
				return
			}
			readmeURL := fmt.Sprintf("%s%s/%s/%s", g.rawURL, fullName, branchName, readmePath)
			g.client.R().SetResponseBodyLimit(1024 * 1024)
			readMeResp, err := g.client.R().Get(readmeURL)
			if err != nil {
				slog.Error("request README.md failed", "full_name", fullName, "err", err)
			} else {
				readMeStr := readMeResp.String()
				mu.Lock()
				g.results[fullName].ReadMe = readMeStr
				mu.Unlock()
			}
		}(item)
	}

	// 等待所有仓库都处理完
	wg.Wait()
	return nil
}

func (g *GithubHandler) QueryLLM() error {
	for fullName, info := range g.results {
		select {
		case <-g.ctx.Done():
			return g.ctx.Err()
		default:
		}

		prompt, err := llm.Prompt(info)
		if err != nil {
			slog.Error("llm prompt failed", slog.String("full.name", fullName), slog.String("vul.id", g.GetVulnName()), slog.String("err", err.Error()))
			continue
		}
		answer, err := g.llm.Query(g.ctx, prompt.String())
		if err != nil {
			slog.Error("query failed", slog.String("full.name", fullName), slog.String("vul.id", g.GetVulnName()), slog.String("err", err.Error()))
			continue
		}
		info.Answer = answer
		if err := info.Handle(); err != nil {
			slog.ErrorContext(g.ctx, "处理响应", "err", err, "repo.name", info.RepositoryName)
			continue
			//return errors.Wrap(err, "repo.name: "+repo.RepositoryName)
		}
		g.results[fullName] = info
		if info.HasPOC {
			g.vuln.HasPOCOrEXP = true
			// 这里只要能找到就不找了，节省token消耗
			// break
		}
	}
	return nil
}

func (g *GithubHandler) Results() (*model.Vulnerability, error) {
	for _, repo := range g.results {
		if _, err := model.SaveOrUpdateVuln(g.db, g.vuln, repo, g.nextRetry, model.SaveRepoRecord); err != nil {
			slog.ErrorContext(g.ctx, "vuln数据更新失败", "err", err, "repo.name", repo.RepositoryName)
			continue
		}
	}

	// 如果没有查询到，同样入库
	if len(g.results) == 0 {
		repo := &results.RepoInfo{VulnName: g.GetVulnName(), SourceType: Github}
		if _, err := model.SaveOrUpdateVuln(g.db, g.vuln, repo, g.nextRetry, model.SaveRepoRecord); err != nil {
			return g.vuln, err
		}
	}

	return g.vuln, nil
}

func (g *GithubHandler) GetName() string {
	return Github
}

func (g *GithubHandler) GetVulnName() string {
	if len(g.vuln.VulnID) != 0 {
		return g.vuln.VulnID
	}
	if len(g.vuln.VulnName) != 0 {
		return g.vuln.VulnName
	}
	return ""
}
