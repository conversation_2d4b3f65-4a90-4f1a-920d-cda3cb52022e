package results

import (
	"errors"
	"log/slog"
	"strings"
)

type RepoInfo struct {
	UserName       string
	RepositoryName string
	RepoLink       string
	Description    string
	ReadMe         string
	Trees          string

	// 用来存放 LLM 的回答
	Answer     string
	VulnName   string
	HasPOC     bool
	FileName   string
	FileLink   string
	Reason     string
	SourceType string
}

func (repo *RepoInfo) Handle() error {
	if len(repo.Answer) == 0 {
		slog.Error("LLM的回答是空的")
		return nil
	}
	// todo: 后续研究结构化格式输出或类似jinja编译模板形式的prompt
	answer := repo.Answer
	ss := strings.Split(answer, "\n")
	idxMap := make(map[string][]int, 4)
	for i, line := range ss {
		if len(line) == 0 {
			continue
		}
		if strings.Contains(line, "Conclusion:") {
			idxMap["conclusion"] = append(idxMap["conclusion"], i)
			continue
		}
		if strings.Contains(line, "POC/EXP File Name") {
			idxMap["conclusion"] = append(idxMap["conclusion"], i-1)
			idxMap["filename"] = append(idxMap["filename"], i)
			continue
		}
		if strings.Contains(line, "Links Found:") {
			idxMap["filename"] = append(idxMap["filename"], i-1)
			idxMap["link"] = append(idxMap["link"], i)
			continue
		}
		if strings.Contains(line, "Brief Judgment Reason:") {
			idxMap["link"] = append(idxMap["link"], i-1)
			idxMap["reason"] = append(idxMap["reason"], i)
			continue
		}
	}

	cs := idxMap["conclusion"]
	if len(cs) < 2 {
		return errors.New("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
	}
	slice := ss[cs[0]:cs[1]]
	for _, s := range slice {
		ls := strings.ToLower(s)
		if strings.Contains(ls, "no") {
			repo.HasPOC = false
			break
		} else if strings.Contains(ls, "yes") {
			repo.HasPOC = true
			break
		}
	}

	fs := idxMap["filename"]
	if len(fs) < 2 {
		return errors.New("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
	}
	slice = ss[fs[0]:fs[1]]
	for _, s := range slice {
		if !repo.HasPOC {
			break
		}
		s = strings.ReplaceAll(s, "**", "")
		s = strings.ReplaceAll(s, "2. POC/EXP File Name (POC/EXP Code):", "")
		s = strings.Replace(s, "-", "", 1)
		s = strings.TrimSpace(s)
		if len(s) > 0 {
			repo.FileName = s
			break
		}
	}

	ls := idxMap["link"]
	if len(ls) < 2 {
		return errors.New("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
	}
	slice = ss[ls[0]:ls[1]]
	for _, s := range slice {
		if !repo.HasPOC {
			break
		}
		s = strings.ReplaceAll(s, "**", "")
		s = strings.ReplaceAll(s, "3. Links Found:", "")
		s = strings.Replace(s, "-", "", 1)
		s = strings.TrimSpace(s)
		if len(s) > 0 {
			repo.FileLink = s
			break
		}
	}

	rs := idxMap["reason"]
	if len(rs) < 1 {
		return errors.New("解析LLM时出错，可能由于LLM回答存在不符合预期格式等问题")
	}
	slice = ss[rs[0]:]
	s := strings.Join(slice, "\n")
	s = strings.ReplaceAll(s, "**", "")
	s = strings.ReplaceAll(s, "4. Brief Judgment Reason:", "")
	s = strings.Replace(s, "-", "", 1)
	s = strings.TrimSpace(s)
	repo.Reason = s

	return nil
}
