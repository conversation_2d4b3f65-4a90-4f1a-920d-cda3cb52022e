package tool

import "regexp"

var (
	vulnReg = regexp.MustCompile(`^(CVE|CNVD)-\d{4}-\d+$`)
)

func IsVulnID(vuln string) bool {
	if vulnReg.MatchString(vuln) {
		return true
	}
	return false
}

// SplitVulns 把一组字符串按 ID/Name 拆分
func SplitVulns(lines []string) (ids, names []string) {
	for _, v := range lines {
		if IsVulnID(v) {
			ids = append(ids, v)
		} else if v != "" {
			names = append(names, v)
		}
	}
	return
}
