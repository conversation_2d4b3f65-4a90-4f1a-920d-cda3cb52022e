package grpc

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	pb "poc-finder/proto"
)

// VulnPusherServer 实现 VulnPusher 服务
type VulnPusherServer struct {
	pb.UnimplementedVulnPusherServer
	vulnHandler VulnHandler
}

// VulnHandler 定义漏洞处理接口
type VulnHandler interface {
	HandleVuln(ctx context.Context, vuln *VulnInfo) error
	HandleInitial(ctx context.Context, initial *InitialInfo) error
	HandleText(ctx context.Context, text string) error
}

// VulnInfo 内部漏洞信息结构
type VulnInfo struct {
	UniqueKey    string
	Title        string
	Description  string
	Severity     string
	CVE          string
	Disclosure   string
	Solutions    string
	GithubSearch []string
	References   []string
	Tags         []string
	From         string
	Reason       []string
	Timestamp    time.Time
}

// InitialInfo 初始化信息结构
type InitialInfo struct {
	Version        string
	VulnCount      int32
	Interval       string
	Provider       []*ProviderInfo
	FailedProvider []*ProviderInfo
	Timestamp      time.Time
}

// ProviderInfo 数据源信息
type ProviderInfo struct {
	Name        string
	DisplayName string
	Link        string
}

// NewVulnPusherServer 创建新的服务实例
func NewVulnPusherServer(handler VulnHandler) *VulnPusherServer {
	return &VulnPusherServer{
		vulnHandler: handler,
	}
}

// PushVuln 处理漏洞推送
func (s *VulnPusherServer) PushVuln(ctx context.Context, req *pb.PushVulnRequest) (*pb.PushVulnResponse, error) {
	// 检查请求参数
	if req.VulnInfo == nil {
		slog.ErrorContext(ctx, "收到空的漏洞信息")
		return &pb.PushVulnResponse{
			Success: false,
			Message: "漏洞信息不能为空",
		}, nil
	}

	slog.InfoContext(ctx, "收到漏洞推送", "title", req.VulnInfo.Title, "cve", req.VulnInfo.Cve)

	// 转换protobuf消息为内部结构
	vulnInfo := &VulnInfo{
		UniqueKey:    req.VulnInfo.UniqueKey,
		Title:        req.VulnInfo.Title,
		Description:  req.VulnInfo.Description,
		Severity:     req.VulnInfo.Severity,
		CVE:          req.VulnInfo.Cve,
		Disclosure:   req.VulnInfo.Disclosure,
		Solutions:    req.VulnInfo.Solutions,
		GithubSearch: req.VulnInfo.GithubSearch,
		References:   req.VulnInfo.References,
		Tags:         req.VulnInfo.Tags,
		From:         req.VulnInfo.From,
		Reason:       req.VulnInfo.Reason,
		Timestamp:    time.Unix(req.Timestamp, 0),
	}

	// 调用业务处理逻辑
	if err := s.vulnHandler.HandleVuln(ctx, vulnInfo); err != nil {
		slog.ErrorContext(ctx, "处理漏洞失败", "error", err, "title", vulnInfo.Title)
		return &pb.PushVulnResponse{
			Success: false,
			Message: fmt.Sprintf("处理失败: %v", err),
		}, nil
	}

	slog.InfoContext(ctx, "成功处理漏洞", "title", vulnInfo.Title)
	return &pb.PushVulnResponse{
		Success: true,
		Message: "漏洞处理成功",
	}, nil
}

// PushInitial 处理WatchVuln初始化消息 - 用于会话建立和状态同步
func (s *VulnPusherServer) PushInitial(ctx context.Context, req *pb.PushInitialRequest) (*pb.PushInitialResponse, error) {
	slog.InfoContext(ctx, "🚀 WatchVuln会话初始化",
		"version", req.InitialMessage.Version,
		"expected_vulns", req.InitialMessage.VulnCount,
		"interval", req.InitialMessage.Interval,
		"active_providers", len(req.InitialMessage.Provider),
		"failed_providers", len(req.InitialMessage.FailedProvider))

	// 记录数据源状态（用于监控和调试）
	for _, provider := range req.InitialMessage.Provider {
		slog.InfoContext(ctx, "✅ 活跃数据源",
			"name", provider.Name,
			"display_name", provider.DisplayName,
			"link", provider.Link)
	}

	for _, provider := range req.InitialMessage.FailedProvider {
		slog.WarnContext(ctx, "❌ 失败数据源",
			"name", provider.Name,
			"display_name", provider.DisplayName,
			"link", provider.Link)
	}

	// 转换为内部结构（如果需要进一步处理）
	initialInfo := &InitialInfo{
		Version:        req.InitialMessage.Version,
		VulnCount:      req.InitialMessage.VulnCount,
		Interval:       req.InitialMessage.Interval,
		Provider:       convertProviders(req.InitialMessage.Provider),
		FailedProvider: convertProviders(req.InitialMessage.FailedProvider),
		Timestamp:      time.Unix(req.Timestamp, 0),
	}

	// 处理初始化逻辑（主要用于记录和监控）
	if err := s.vulnHandler.HandleInitial(ctx, initialInfo); err != nil {
		slog.ErrorContext(ctx, "初始化处理失败", "error", err)
		return &pb.PushInitialResponse{
			Success: false,
			Message: fmt.Sprintf("初始化失败: %v", err),
		}, nil
	}

	slog.InfoContext(ctx, "✅ WatchVuln会话初始化完成",
		"ready_to_receive", true,
		"expected_vulns", req.InitialMessage.VulnCount)

	return &pb.PushInitialResponse{
		Success: true,
		Message: fmt.Sprintf("POC-Finder已就绪，等待接收 %d 个漏洞", req.InitialMessage.VulnCount),
	}, nil
}

// convertProviders 转换Provider信息的辅助函数
func convertProviders(providers []*pb.Provider) []*ProviderInfo {
	result := make([]*ProviderInfo, len(providers))
	for i, p := range providers {
		result[i] = &ProviderInfo{
			Name:        p.Name,
			DisplayName: p.DisplayName,
			Link:        p.Link,
		}
	}
	return result
}

// PushText 处理WatchVuln状态消息推送 - 用于状态通知和日志记录
func (s *VulnPusherServer) PushText(ctx context.Context, req *pb.PushTextRequest) (*pb.PushTextResponse, error) {
	// 根据消息内容确定日志级别
	message := req.Message
	timestamp := time.Unix(req.Timestamp, 0)

	// 分析消息类型并记录相应级别的日志
	if isErrorMessage(message) {
		slog.ErrorContext(ctx, "🚨 WatchVuln错误消息",
			"message", message,
			"timestamp", timestamp)
	} else if isWarningMessage(message) {
		slog.WarnContext(ctx, "⚠️ WatchVuln警告消息",
			"message", message,
			"timestamp", timestamp)
	} else {
		slog.InfoContext(ctx, "📝 WatchVuln状态消息",
			"message", message,
			"timestamp", timestamp)
	}

	// 处理特殊状态消息
	if err := s.vulnHandler.HandleText(ctx, message); err != nil {
		slog.ErrorContext(ctx, "状态消息处理失败", "error", err, "message", message)
		return &pb.PushTextResponse{
			Success: false,
			Message: fmt.Sprintf("状态消息处理失败: %v", err),
		}, nil
	}

	return &pb.PushTextResponse{
		Success: true,
		Message: "状态消息已记录",
	}, nil
}

// isErrorMessage 判断是否为错误消息
func isErrorMessage(message string) bool {
	errorKeywords := []string{"错误", "error", "failed", "失败", "异常", "exception"}
	messageLower := strings.ToLower(message)
	for _, keyword := range errorKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}
	return false
}

// isWarningMessage 判断是否为警告消息
func isWarningMessage(message string) bool {
	warningKeywords := []string{"警告", "warning", "warn", "注意", "超时", "timeout"}
	messageLower := strings.ToLower(message)
	for _, keyword := range warningKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}
	return false
}
