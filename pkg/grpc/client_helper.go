package grpc

import (
	"context"
	"fmt"
	"net"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
)

// ClientOptions gRPC客户端选项
type ClientOptions struct {
	// Target 连接目标地址
	Target string
	// Timeout 连接超时时间
	Timeout time.Duration
	// UseInsecure 是否使用不安全连接
	UseInsecure bool
	// ContextDialer 自定义拨号器（主要用于测试）
	ContextDialer func(context.Context, string) (net.Conn, error)
	// WaitForReady 是否等待连接就绪（替代已弃用的WithBlock）
	WaitForReady bool
}

// NewClientConnection 创建gRPC客户端连接
// 这个函数封装了新旧API的兼容性处理
func NewClientConnection(opts ClientOptions) (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(context.Background(), opts.Timeout)
	defer cancel()

	// 准备连接选项
	var dialOpts []grpc.DialOption

	// 添加认证选项
	if opts.UseInsecure {
		dialOpts = append(dialOpts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	}

	// 添加自定义拨号器（主要用于测试环境）
	if opts.ContextDialer != nil {
		// 对于测试环境，我们仍然使用已弃用的API以确保兼容性
		// 这是因为新的API在bufconn环境下存在已知问题
		//nolint:staticcheck // 在测试环境中使用已弃用的API以确保兼容性
		dialOpts = append(dialOpts, grpc.WithContextDialer(opts.ContextDialer))

		if opts.WaitForReady {
			//nolint:staticcheck // 在测试环境中使用已弃用的API以确保兼容性
			dialOpts = append(dialOpts, grpc.WithBlock())
		}

		// 使用已弃用的DialContext用于测试环境
		//nolint:staticcheck // 在测试环境中使用已弃用的API以确保兼容性
		return grpc.DialContext(ctx, opts.Target, dialOpts...)
	}

	// 对于生产环境，使用新的API
	conn, err := grpc.NewClient(opts.Target, dialOpts...)
	if err != nil {
		return nil, fmt.Errorf("创建gRPC客户端失败: %w", err)
	}

	// 如果需要等待连接就绪，手动处理连接状态
	if opts.WaitForReady {
		if err := waitForConnection(ctx, conn); err != nil {
			conn.Close()
			return nil, err
		}
	}

	return conn, nil
}

// waitForConnection 等待连接就绪（替代已弃用的WithBlock功能）
func waitForConnection(ctx context.Context, conn *grpc.ClientConn) error {
	// 触发连接
	conn.Connect()

	// 等待连接状态变为Ready
	for {
		state := conn.GetState()
		switch state {
		case connectivity.Ready:
			return nil
		case connectivity.Shutdown:
			return fmt.Errorf("连接已关闭")
		case connectivity.TransientFailure:
			// 对于瞬时失败，我们继续等待重试
		}

		// 等待状态变化或超时
		if !conn.WaitForStateChange(ctx, state) {
			return fmt.Errorf("等待连接超时")
		}

		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}
	}
}

// DefaultClientOptions 返回默认的客户端选项
func DefaultClientOptions(target string) ClientOptions {
	return ClientOptions{
		Target:       target,
		Timeout:      5 * time.Second,
		UseInsecure:  true,
		WaitForReady: true,
	}
}

// TestClientOptions 返回测试环境的客户端选项
func TestClientOptions(target string, dialer func(context.Context, string) (net.Conn, error)) ClientOptions {
	return ClientOptions{
		Target:        target,
		Timeout:       5 * time.Second,
		UseInsecure:   true,
		ContextDialer: dialer,
		WaitForReady:  true,
	}
}
