package grpc

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"gorm.io/gorm"

	"poc-finder/config"
	"poc-finder/handler"
	"poc-finder/model"
	"poc-finder/pkg/results"
	"poc-finder/pkg/tool"
)

// IntegratedVulnHandler 集成的漏洞处理器，复用现有的POC搜索逻辑
type IntegratedVulnHandler struct {
	cfg *config.Config
}

// NewIntegratedVulnHandler 创建集成的漏洞处理器
func NewIntegratedVulnHandler(cfg *config.Config) *IntegratedVulnHandler {
	return &IntegratedVulnHandler{
		cfg: cfg,
	}
}

// HandleVuln 处理漏洞信息 - 存储+搜索模式，使用统一的调度逻辑
func (h *IntegratedVulnHandler) HandleVuln(ctx context.Context, vuln *VulnInfo) error {
	// 0. 输入验证
	if vuln == nil {
		return fmt.Errorf("漏洞信息为空")
	}

	slog.InfoContext(ctx, "🎯 接收WatchVuln漏洞推送，开始存储+搜索流程",
		"title", vuln.Title,
		"cve", vuln.CVE,
		"severity", vuln.Severity,
		"unique_key", vuln.UniqueKey,
		"from", vuln.From,
		"tags", vuln.Tags)

	// 1. 标准化漏洞标识符
	vulnID := vuln.CVE
	vulnName := vuln.Title

	// 如果没有CVE，使用UniqueKey作为主要标识符
	if vulnID == "" {
		vulnID = vuln.UniqueKey
	}
	if vulnName == "" {
		vulnName = vuln.Title
	}

	// 验证必要字段
	if vulnID == "" && vulnName == "" {
		return fmt.Errorf("漏洞记录缺少有效标识符: CVE=%s, Title=%s, UniqueKey=%s",
			vuln.CVE, vuln.Title, vuln.UniqueKey)
	}

	// 2. 保存漏洞基础信息（使用UPSERT，自动处理去重）
	isNew, err := h.saveVulnRecord(ctx, vuln)
	if err != nil {
		return fmt.Errorf("保存漏洞记录失败: %w", err)
	}

	// 3. 记录存储结果
	if isNew {
		slog.InfoContext(ctx, "✅ 新漏洞记录已存储",
			"vuln_id", vulnID,
			"vuln_name", vulnName,
			"from", vuln.From,
			"tags_count", len(vuln.Tags))
	} else {
		slog.InfoContext(ctx, "🔄 漏洞记录已更新",
			"vuln_id", vulnID,
			"vuln_name", vulnName,
			"from", vuln.From,
			"tags_count", len(vuln.Tags))
	}

	// 4. 触发POC搜索（使用统一的调度逻辑）
	if err = h.triggerPocSearch(ctx, vuln); err != nil {
		slog.ErrorContext(ctx, "POC搜索触发失败", "error", err, "vuln_id", vulnID)
		// 不返回错误，允许继续处理其他漏洞
	}

	return nil
}

// HandleInitial 处理WatchVuln初始化消息 - 记录会话状态和监控信息
func (h *IntegratedVulnHandler) HandleInitial(ctx context.Context, initial *InitialInfo) error {
	slog.InfoContext(ctx, "📊 WatchVuln会话状态记录",
		"version", initial.Version,
		"expected_vulns", initial.VulnCount,
		"scan_interval", initial.Interval,
		"active_sources", len(initial.Provider),
		"failed_sources", len(initial.FailedProvider),
		"session_time", initial.Timestamp)

	// 记录活跃数据源（用于监控）
	activeSourceNames := make([]string, len(initial.Provider))
	for i, provider := range initial.Provider {
		activeSourceNames[i] = provider.Name
		slog.DebugContext(ctx, "✅ 活跃数据源详情",
			"name", provider.Name,
			"display_name", provider.DisplayName,
			"link", provider.Link)
	}

	// 记录失败数据源（用于告警）
	failedSourceNames := make([]string, len(initial.FailedProvider))
	for i, provider := range initial.FailedProvider {
		failedSourceNames[i] = provider.Name
		slog.WarnContext(ctx, "❌ 失败数据源详情",
			"name", provider.Name,
			"display_name", provider.DisplayName,
			"link", provider.Link)
	}

	// 汇总记录（便于监控系统解析）
	slog.InfoContext(ctx, "📈 数据源状态汇总",
		"active_sources", activeSourceNames,
		"failed_sources", failedSourceNames,
		"success_rate", float64(len(initial.Provider))/float64(len(initial.Provider)+len(initial.FailedProvider)))

	return nil
}

// HandleText 处理WatchVuln状态文本消息 - 用于状态监控和异常处理
func (h *IntegratedVulnHandler) HandleText(ctx context.Context, text string) error {
	// 分析消息内容并采取相应行动
	textLower := strings.ToLower(text)

	// 处理关键状态消息
	switch {
	case strings.Contains(textLower, "进程退出") || strings.Contains(textLower, "process exit"):
		slog.WarnContext(ctx, "🔄 WatchVuln进程退出", "message", text)
		// 可以在这里执行清理逻辑或发送告警

	case strings.Contains(textLower, "扫描完成") || strings.Contains(textLower, "scan completed"):
		slog.InfoContext(ctx, "✅ WatchVuln扫描周期完成", "message", text)

	case strings.Contains(textLower, "开始扫描") || strings.Contains(textLower, "scan started"):
		slog.InfoContext(ctx, "🚀 WatchVuln开始新扫描周期", "message", text)

	case strings.Contains(textLower, "错误") || strings.Contains(textLower, "error"):
		slog.ErrorContext(ctx, "🚨 WatchVuln报告错误", "message", text)

	case strings.Contains(textLower, "警告") || strings.Contains(textLower, "warning"):
		slog.WarnContext(ctx, "⚠️ WatchVuln警告", "message", text)

	default:
		slog.InfoContext(ctx, "📝 WatchVuln状态消息", "message", text)
	}

	return nil
}

// saveVulnRecord 高效保存漏洞记录，专门用于WatchVuln推送的数据存储
// 返回值：是否为新记录，错误信息
func (h *IntegratedVulnHandler) saveVulnRecord(ctx context.Context, vuln *VulnInfo) (bool, error) {
	// 1. 构建漏洞记录
	vulnerability := &model.Vulnerability{
		VulnID:      vuln.CVE,
		VulnName:    vuln.Title,
		Description: vuln.Description,
		Tag:         strings.Join(vuln.Tags, ","),
		HasPOCOrEXP: false, // WatchVuln推送的是新发现的漏洞，初始状态为false
		NextRetry:   time.Now().Add(h.cfg.Source.NextRetry * 24 * time.Hour),
	}

	// 如果没有CVE，使用UniqueKey
	if vulnerability.VulnID == "" {
		vulnerability.VulnID = vuln.UniqueKey
	}

	// 2. 检查是否为新记录（用于日志记录）
	var existingCount int64
	h.cfg.DB.Model(&model.Vulnerability{}).
		Where("vuln_id = ? OR vuln_name = ?", vulnerability.VulnID, vulnerability.VulnName).
		Count(&existingCount)
	isNew := existingCount == 0

	// 3. 使用UPSERT操作保存（自动处理去重）
	_, err := model.SaveOrUpdateVuln(h.cfg.DB, vulnerability, nil, h.cfg.Source.NextRetry*24*time.Hour,
		func(db *gorm.DB, v *model.Vulnerability, repo *results.RepoInfo, now time.Time) error {
			// WatchVuln推送的是漏洞基础信息，不包含具体的POC记录
			// 这里不需要保存repo记录
			return nil
		})

	if err != nil {
		return false, fmt.Errorf("数据库保存失败: %w", err)
	}

	// 4. 记录详细信息
	slog.InfoContext(ctx, "漏洞记录保存成功",
		"vuln_id", vulnerability.VulnID,
		"vuln_name", vulnerability.VulnName,
		"is_new", isNew,
		"tags", vulnerability.Tag,
		"from", vuln.From)

	return isNew, nil
}

// triggerPocSearch 触发POC搜索 - 使用全局限流器的统一调度逻辑
// 接收完整的VulnInfo结构体，为处理器提供更丰富的上下文信息
func (h *IntegratedVulnHandler) triggerPocSearch(ctx context.Context, vulnInfo *VulnInfo) error {
	// 提取基本标识符
	vulnID := vulnInfo.CVE
	vulnName := vulnInfo.Title

	// 如果没有CVE，使用UniqueKey作为主要标识符
	if vulnID == "" {
		vulnID = vulnInfo.UniqueKey
	}

	slog.InfoContext(ctx, "🔍 开始POC搜索",
		"vuln_id", vulnID,
		"vuln_name", vulnName,
		"tags", vulnInfo.Tags,
		"severity", vulnInfo.Severity,
		"disclosure", vulnInfo.Disclosure)

	// 判断漏洞类型和输入值
	vulnType := "Name"
	vulnInput := vulnName
	if tool.IsVulnID(vulnID) {
		vulnType = "ID"
		vulnInput = vulnID
	}

	// 创建增强的漏洞任务，包含完整的上下文信息
	task := handler.VulnTask{
		Value:        vulnInput,
		Type:         vulnType,
		Tags:         vulnInfo.Tags,
		Severity:     vulnInfo.Severity,
		Disclosure:   vulnInfo.Disclosure,
		References:   vulnInfo.References,
		GithubSearch: vulnInfo.GithubSearch,
		Description:  vulnInfo.Description,
		UniqueKey:    vulnInfo.UniqueKey,
		From:         vulnInfo.From,
	}

	// 使用全局限流器的统一调度逻辑处理任务
	vuln := handler.ProcessVulnTask(ctx, h.cfg, task)

	if vuln != nil && vuln.HasPOCOrEXP {
		// 发送通知
		if err := h.cfg.Notifier.NotifyVulnerability(vuln); err != nil {
			slog.ErrorContext(ctx, "💬 POC发现通知失败", "error", err, "vuln_id", vulnID)
		} else {
			slog.InfoContext(ctx, "🎉 发现POC并已通知", "vuln_id", vulnID, "vuln_name", vulnName)
		}
	} else {
		slog.InfoContext(ctx, "🔍 POC搜索完成，未发现POC", "vuln_id", vulnID, "vuln_name", vulnName)
	}

	return nil
}
