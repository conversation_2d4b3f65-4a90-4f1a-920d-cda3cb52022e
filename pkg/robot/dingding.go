package robot

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"html/template"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"poc-finder/model"
)

type DingMarkdown struct {
	MsgType  string `json:"msgtype"`
	Markdown struct {
		Title string `json:"title"`
		Text  string `json:"text"`
	} `json:"markdown"`
}

type DingTalkNotifier struct {
	accessToken string
	secret      string
}

func NewDingTalkNotifier(accessToken, secret string) *DingTalkNotifier {
	return &DingTalkNotifier{
		accessToken: accessToken,
		secret:      secret,
	}
}

func (d *DingTalkNotifier) NotifyVulnerability(v *model.Vulnerability) error {
	webhook := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s", d.accessToken)
	if len(d.secret) != 0 {
		var err error
		webhook, err = signURL(webhook, d.secret)
		if err != nil {
			return err
		}
	}

	msg := &DingMarkdown{MsgType: "markdown"}
	md, err := renderVulnMarkdown(v)
	if err != nil {
		return err
	}
	msg.Markdown = struct {
		Title string `json:"title"`
		Text  string `json:"text"`
	}{Title: fmt.Sprintf("漏洞: %s", v.VulnID), Text: md}

	b, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	resp, err := http.Post(webhook, "application/json", bytes.NewBuffer(b))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("非 200 响应 %d", resp.StatusCode)
	}
	return nil
}

func signURL(webhook, secret string) (string, error) {
	ts := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	stringToSign := ts + "\n" + secret

	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write([]byte(stringToSign))
	signData := mac.Sum(nil)
	sign := url.QueryEscape(base64.StdEncoding.EncodeToString(signData))

	return fmt.Sprintf("%s&timestamp=%s&sign=%s", webhook, ts, sign), nil
}

func renderVulnMarkdown(v *model.Vulnerability) (string, error) {
	// 1. 定义 FuncMap，用于日期格式优化
	funcMap := template.FuncMap{
		"fmtDate": func(t time.Time) string {
			return t.Format("2006-01-02")
		},
		"add": func(a, b int) int {
			return a + b
		},
		"blankIfNil": func(s string) string {
			if s == "" {
				return ""
			}
			return fmt.Sprintf(" | %s", s)
		},
	}

	// 2. Markdown 模板
	const mdTemp = `
## 🚨 漏洞发现：{{ .VulnID }}{{blankIfNil .VulnName }}
- **描述**：{{ .Description }}
- **标签**：{{ .Tag }}
- **首次披露**：{{ fmtDate .ClosureTime }}

### 仓库 && POC 列表
{{- range $idx, $repo := .Records }}
{{ add $idx 1 }}. [{{ $repo.RepoName }}]({{ $repo.RepoLink }}) | [POC：{{ $repo.POCFile }}]({{ $repo.POCLink }})
   来源：{{ $repo.SourceType }}
{{- end }}
> （共 {{ len .Records }} 条）
`
	// 3. 创建并解析模板
	temp, err := template.New("vulnMD").
		Funcs(funcMap).
		Parse(mdTemp)
	if err != nil {
		return "", err
	}

	// 4. 执行模板
	var buf bytes.Buffer
	if err := temp.Execute(&buf, v); err != nil {
		return "", err
	}
	return buf.String(), nil
}
