package scheduler

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"os"
	"time"

	"github.com/radovskyb/watcher"
	"gorm.io/gorm"

	"poc-finder/config"
	"poc-finder/model"
)

// FetchDueVulns 返回所有 has_poc_or_exp = false 且 next_retry <= now 的漏洞
func FetchDueVulns(db *gorm.DB) ([]model.Vulnerability, error) {
	var vulns []model.Vulnerability
	err := db.
		Where("has_poc_or_exp = ? AND next_retry <= ?", false, time.Now()).
		Find(&vulns).Error
	if err != nil {
		return nil, fmt.Errorf("FetchDueVulns 查询失败: %w", err)
	}
	return vulns, nil
}

// NextRunTime 返回最早的 next_retry 时间，用于调度下一轮醒来时机
func NextRunTime(db *gorm.DB) (time.Time, error) {
	var t sql.NullTime
	// SELECT MIN(next_retry) FROM vulnerabilities WHERE has_poc_or_exp = false
	err := db.
		Model(&model.Vulnerability{}).
		Select("MIN(next_retry) AS MIN").
		Where("has_poc_or_exp = ?", false).
		Scan(&t).Error
	if err != nil {
		return time.Time{}, err
	}
	// 如果表中没有符合条件的记录，返回当前时间，立即触发
	if !t.Valid {
		return time.Time{}, nil
	}
	return t.Time, nil
}

// UpdateVulnAfterScan 扫描完成后更新漏洞记录：
// - 如果有任何 Record.HasPOC = true，则设置 has_poc_or_exp = true
// - 否则更新 next_retry 为 now + retryInterval
// 并统一更新 last_checked
func UpdateVulnAfterScan(db *gorm.DB, vulID string, retryInterval time.Duration) error {
	// 统计是否已有 POC
	var count int64
	err := db.
		Model(&model.Record{}).
		Where("vul_id = ? AND has_poc = ?", vulID, true).
		Count(&count).Error
	if err != nil {
		return fmt.Errorf("UpdateVulnAfterScan Count 失败: %w", err)
	}

	now := time.Now()
	updates := map[string]interface{}{
		"last_checked": now,
	}
	if count > 0 {
		updates["has_poc_or_exp"] = true
	} else {
		updates["next_retry"] = now.Add(retryInterval)
	}

	err = db.
		Model(&model.Vulnerability{}).
		Where("vul_id = ?", vulID).
		Updates(updates).Error
	if err != nil {
		return fmt.Errorf("UpdateVulnAfterScan 更新失败: %w", err)
	}
	return nil
}

// Filter 过滤掉已经查找到POC的漏洞记录，返回需要扫描的漏洞ID和名称
func Filter(db *gorm.DB, vulnIDs, vulnNames []string) (filteredIDs, filteredNames []string, err error) {
	// 如果输入为空，直接返回
	if len(vulnIDs) == 0 && len(vulnNames) == 0 {
		return vulnIDs, vulnNames, nil
	}

	var vulns []model.Vulnerability
	query := db.Select("vuln_id, vuln_name").Where("has_poc_or_exp = ?", true)

	// 构建查询条件
	if len(vulnIDs) > 0 && len(vulnNames) > 0 {
		query = query.Where("vuln_id IN ? OR vuln_name IN ?", vulnIDs, vulnNames)
	} else if len(vulnIDs) > 0 {
		query = query.Where("vuln_id IN ?", vulnIDs)
	} else {
		query = query.Where("vuln_name IN ?", vulnNames)
	}

	// 执行查询，获取已有POC的漏洞
	if err = query.Find(&vulns).Error; err != nil {
		return nil, nil, fmt.Errorf("filter 查询失败: %w", err)
	}

	// 构建已有POC的漏洞映射
	existingIDs := make(map[string]struct{})
	existingNames := make(map[string]struct{})
	for _, vuln := range vulns {
		if vuln.VulnID != "" {
			existingIDs[vuln.VulnID] = struct{}{}
		}
		if vuln.VulnName != "" {
			existingNames[vuln.VulnName] = struct{}{}
		}
	}

	// 过滤掉已有POC的漏洞ID
	for _, id := range vulnIDs {
		if _, exists := existingIDs[id]; !exists {
			filteredIDs = append(filteredIDs, id)
		}
	}

	// 过滤掉已有POC的漏洞名称
	for _, name := range vulnNames {
		if _, exists := existingNames[name]; !exists {
			filteredNames = append(filteredNames, name)
		}
	}

	return filteredIDs, filteredNames, nil
}

// FileUpdate 代表一次文件变更后读取到的结果
type FileUpdate struct {
	Content []byte
	Err     error
}

// WatchFile 监控 path 指定的文件，当它被写入或创建时，
// 在防抖时间（debounce）后读取最新内容并推送到返回的 channel。
// 上下文取消后自动停止并关闭 channel。
func WatchFile(cfg *config.Config, ctx context.Context, path string) <-chan FileUpdate {
	updates := make(chan FileUpdate)
	w := cfg.Watcher

	// 关心文件写入、创建和删除
	w.FilterOps(watcher.Write, watcher.Create, watcher.Remove)

	// 注册要监控的文件
	if err := w.Add(path); err != nil {
		go func() {
			updates <- FileUpdate{nil, err}
			close(updates)
		}()
		return updates
	}

	// 启动 watcher（Interval 可根据平台调整）
	go func() {
		defer close(updates)
		go func() {
			// Stop watcher when context is done
			<-ctx.Done()
			w.Close()
		}()
		if err := w.Start(1 * time.Second); err != nil {
			updates <- FileUpdate{nil, err}
			return
		}
	}()

	// 监听事件
	go func() {
		for {
			select {
			case event, ok := <-w.Event:
				if !ok {
					return
				}
				slog.InfoContext(ctx, "filewatch: 侦测到文件变动\n", "文件名", event.Name(), "变动时间", event.ModTime())
				data, err := os.ReadFile(path)
				updates <- FileUpdate{data, err}

			case err, ok := <-w.Error:
				if !ok {
					return
				}
				updates <- FileUpdate{nil, err}

			case <-ctx.Done():
				return
			}
		}
	}()

	return updates
}
