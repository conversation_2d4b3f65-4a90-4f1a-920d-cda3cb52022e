package scheduler

import (
	"context"
	"log/slog"
	"time"

	"poc-finder/config"
	"poc-finder/handler"
)

// TaskLevelScheduler 任务级别调度器，用于控制任务启动频率和并发数
type TaskLevelScheduler struct {
	cfg           *config.Config
	ctx           context.Context
	batchSize     int
	batchInterval time.Duration
	maxConcurrent int
	taskDelay     time.Duration
	enableLimit   bool
}

// VulnTask 漏洞任务结构体（与cmd/main.go中的VulnTask保持一致）
type VulnTask struct {
	Value string   // 漏洞标识符或名称
	Type  string   // 任务类型：ID 或 Name
	Tags  []string // 标签列表
}

// NewTaskLevelScheduler 创建任务级别调度器
func NewTaskLevelScheduler(cfg *config.Config, ctx context.Context) *TaskLevelScheduler {
	return &TaskLevelScheduler{
		cfg:           cfg,
		ctx:           ctx,
		batchSize:     cfg.TaskScheduling.BatchSize,
		batchInterval: cfg.TaskScheduling.GetBatchIntervalDuration(),
		//maxConcurrent: cfg.TaskScheduling.MaxConcurrent,
		taskDelay:   cfg.TaskScheduling.GetTaskStartDelayDuration(),
		enableLimit: cfg.TaskScheduling.EnableRateLimit,
	}
}

// ProcessTasks 处理任务列表，支持任务级别限流
func (tls *TaskLevelScheduler) ProcessTasks(tasks []VulnTask) {
	if len(tasks) == 0 {
		return
	}

	// 转换为handler任务格式
	handlerTasks := tls.convertToHandlerTasks(tasks)

	// 如果未启用限流，直接处理
	if !tls.enableLimit {
		slog.Info("任务级别限流未启用，直接处理所有任务", "count", len(handlerTasks))
		handler.ProcessVulnTasks(tls.ctx, tls.cfg, handlerTasks)
		return
	}

	slog.Info("🚀 开始任务级别限流处理",
		"total_tasks", len(handlerTasks),
		"batch_size", tls.batchSize,
		"batch_interval", tls.batchInterval,
		"max_concurrent", tls.maxConcurrent)

	// 分批处理
	batches := tls.createBatches(handlerTasks)
	tls.processBatches(batches)
}

// ProcessTasksDirect 直接处理任务（不使用限流，用于gRPC和search命令）
func (tls *TaskLevelScheduler) ProcessTasksDirect(tasks []VulnTask) {
	if len(tasks) == 0 {
		return
	}

	handlerTasks := tls.convertToHandlerTasks(tasks)
	slog.Info("直接处理任务", "count", len(handlerTasks))
	handler.ProcessVulnTasks(tls.ctx, tls.cfg, handlerTasks)
}

// createBatches 将任务分批
func (tls *TaskLevelScheduler) createBatches(tasks []handler.VulnTask) [][]handler.VulnTask {
	var batches [][]handler.VulnTask

	for i := 0; i < len(tasks); i += tls.batchSize {
		end := i + tls.batchSize
		if end > len(tasks) {
			end = len(tasks)
		}
		batches = append(batches, tasks[i:end])
	}

	slog.Debug("任务分批完成", "total_tasks", len(tasks), "batches", len(batches))
	return batches
}

// processBatches 处理批次，支持并发控制和间隔控制
func (tls *TaskLevelScheduler) processBatches(batches [][]handler.VulnTask) {
	if len(batches) == 0 {
		return
	}

	for i, batch := range batches {
		// 批次启动延迟（除了第一批）
		if i > 0 {
			delay := time.Duration(i) * tls.batchInterval
			slog.Debug("批次启动延迟", "batch", i+1, "delay", delay)

			select {
			case <-time.After(delay):
			case <-tls.ctx.Done():
				slog.Info("任务调度被取消", "batch", i+1)
				return
			}
		}

		slog.Info("📦 处理任务批次",
			"batch", i+1,
			"total_batches", len(batches),
			"tasks_in_batch", len(batch))

		// 处理当前批次（使用全局限流器）
		handler.ProcessVulnTasks(tls.ctx, tls.cfg, batch)

		slog.Info("✅ 批次处理完成", "batch", i+1)
	}
	slog.Info("🎉 任务级别限流处理完成", "total_batches", len(batches))
}

// convertToHandlerTasks 转换任务格式
func (tls *TaskLevelScheduler) convertToHandlerTasks(tasks []VulnTask) []handler.VulnTask {
	handlerTasks := make([]handler.VulnTask, len(tasks))
	for i, task := range tasks {
		handlerTasks[i] = handler.VulnTask{
			Value: task.Value,
			Type:  task.Type,
			Tags:  task.Tags,
		}
	}
	return handlerTasks
}

// GetSchedulerStatus 获取调度器状态信息
func (tls *TaskLevelScheduler) GetSchedulerStatus() map[string]interface{} {
	return map[string]interface{}{
		"enabled":        tls.enableLimit,
		"batch_size":     tls.batchSize,
		"batch_interval": tls.batchInterval.String(),
		"max_concurrent": tls.maxConcurrent,
		"task_delay":     tls.taskDelay.String(),
	}
}

// UpdateConfig 更新调度器配置（用于配置热重载）
func (tls *TaskLevelScheduler) UpdateConfig(cfg *config.Config) {
	tls.cfg = cfg
	tls.batchSize = cfg.TaskScheduling.BatchSize
	tls.batchInterval = cfg.TaskScheduling.GetBatchIntervalDuration()
	//tls.maxConcurrent = cfg.TaskScheduling.MaxConcurrent
	tls.taskDelay = cfg.TaskScheduling.GetTaskStartDelayDuration()
	tls.enableLimit = cfg.TaskScheduling.EnableRateLimit

	slog.Info("任务调度器配置已更新",
		"batch_size", tls.batchSize,
		"batch_interval", tls.batchInterval,
		"max_concurrent", tls.maxConcurrent,
		"enable_limit", tls.enableLimit)
}
