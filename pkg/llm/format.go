package llm

import "encoding/json"

type JSONFormat struct {
	Title      string `json:"title"`
	Type       string `json:"type"`
	Properties struct {
		Conclusion struct {
			Type        string `json:"type"`
			Description string `json:"description"`
		} `json:"conclusion"`
		PocName struct {
			Type        string `json:"type"`
			Description string `json:"description"`
		} `json:"poc_name"`
		Links struct {
			Type        string `json:"type"`
			Description string `json:"description"`
			Items       struct {
				Type   string `json:"type"`
				Format string `json:"format"`
			} `json:"items"`
		} `json:"links"`
		Reason struct {
			Type        string `json:"type"`
			Description string `json:"description"`
		} `json:"reason"`
	} `json:"properties"`
	Required             []string `json:"required"`
	AdditionalProperties bool     `json:"additionalProperties"`
}

var SchemaJSON = `{
      "title": "poc_finder",
      "type": "object",
      "properties": {
        "conclusion": {
          "type": "string",
          "description": "Given the following GitHub repository information, determine whether it contains a proof-of-concept (POC) or exploit (EXP) for the above target vulnerability name"
        },
        "poc_name": {
          "type": "string",
          "description": "If the conclusion is \"yes\", give the most likely file name (should be one of examples)"
        },
        "links": {
          "type": "array",
          "description": "If you can piece together the full URL of the file on GitHub, please give it. If no links found, keep it blank. For example:\n\"https://github.com/username/repo/blob/main/exploit.py\"",
          "items": {
            "type": "string",
            "format": "uri"
          }
        },
        "reason": {
          "type": "string",
          "description": "Combine the keywords that appear in the repo name, directory, README (such as \"poc\", \"exploit\", \"cve\", compilation/execution examples) to explain your judgment."
        }
      },
      "required": ["conclusion", "poc_name", "links", "reason"],
      "additionalProperties": false
    }`

func NewJSONFormat() *JSONFormat {
	var j JSONFormat
	_ = j.UnmarshalJSON([]byte(SchemaJSON))
	return &j
}

func (j *JSONFormat) MarshalJSON() ([]byte, error) {
	return json.Marshal(SchemaJSON)
}

func (j *JSONFormat) UnmarshalJSON(data []byte) error {
	return json.Unmarshal(data, j)
}
