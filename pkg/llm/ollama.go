package llm

import (
	"context"
	"strings"

	"github.com/ollama/ollama/api"
)

type Ollama struct {
	Model string
}

func (o *Ollama) Query(ctx context.Context, prompt string) (string, error) {
	client, err := api.ClientFromEnvironment()
	if err != nil {
		return "", err
	}
	messages := []api.Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	req := &api.ChatRequest{
		Model:    o.Model,
		Messages: messages,
	}

	var sb strings.Builder
	respFunc := func(response api.ChatResponse) error {
		sb.WriteString(response.Message.Content)
		return nil
	}

	if err = client.Chat(ctx, req, respFunc); err != nil {
		return "", err
	}
	return sb.String(), nil
}
