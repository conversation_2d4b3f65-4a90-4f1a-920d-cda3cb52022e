package llm

import (
	"fmt"
	"strings"

	"poc-finder/pkg/results"
)

type AnswerFormat struct {
	conclusion string
	fileName   string
	links      []string
	reason     string
}

func Prompt(info *results.RepoInfo) (*strings.Builder, error) {
	input := make([]string, 6)
	input[0] = info.VulnName
	input[1] = info.UserName
	input[2] = info.RepositoryName
	input[3] = info.Description
	input[4] = info.Trees
	input[5] = info.ReadMe
	p, err := prompt(input)
	if err != nil {
		return nil, err
	}
	return p, nil
}

func prompt(input []string) (*strings.Builder, error) {
	if len(input) < 6 {
		return nil, fmt.Errorf("调用Query的输入参数数组元素不足6个")
	}

	CVEID := input[0]
	UserName := input[1]
	RepoName := input[2]
	Description := input[3]
	Trees := input[4]
	ReadMe := input[5]
	question := fmt.Sprintf(`
Target Vulnerability Name: %s
Given the following GitHub repository information, determine whether it contains a proof-of-concept (POC) or exploit (EXP) for the above target vulnerability name.
Select one of the files that most likely refers to the vulnerability.
Exclude keywords dashboard, panel.
Tips:
It is necessary to pay attention to the relationship between folders and file paths in the Directory structure
- The folder name of a possible POC or EXP is generally the name of the product affected by the vulnerability
- If the input ID keyword is included in the single file name, it is likely to be the target POC or EXP
——————
[Basic repository information]
User Name: %s
Repository Name: %s
Repository Description: %s
——————
[Directory structure (path, url, size are listed. A size of 0 indicates a folder)]
%s
——————
【README.md content】
%s`, CVEID, UserName, RepoName, Description, Trees, ReadMe)
	expectedFormat := fmt.Sprintf(`
——————
Based on the above information, please output the conclusion in the following format:

1. Conclusion:
- "Yes" or "No"

2. POC/EXP File Name (POC/EXP Code):
- If the conclusion is "yes", give the most likely file name (should be one of examples)

3. Links Found:
- If you can piece together the full URL of the file on GitHub, please give it. If no links found, keep it blank. For example:
	"https://github.com/username/warehouse_name/blob/main/exploit.py"
	"https://github.com/username/warehouse_name/blob/main/poc/example.c"

4. Brief Judgment Reason:
- Combine the keywords that appear in the warehouse name, directory, README (such as "poc", "exploit", "cve", compilation/execution examples) to explain your judgment.
- For example: "There is a folder called 'poc' in the repository directory, there is an example description of 'Usage: python exploit.py' in the README, and the file name contains 'exploit', so it is highly suspected to be POC/EXP."

——————————————————————————————
Please answer completely according to the above format.`)
	prompt := strings.Builder{}
	prompt.WriteString(question)
	prompt.WriteString("\n")
	prompt.WriteString(expectedFormat)

	return &prompt, nil
}
