package llm

import (
	"context"
	"encoding/json"
	"log/slog"
	"os"

	"github.com/revrost/go-openrouter"
)

type OpenRouter struct {
	Token string
	Model string
}

func (o *OpenRouter) Query(ctx context.Context, prompt string) (string, error) {
	return o.routerQuery(prompt, o.Model)
}

func (o *OpenRouter) routerQuery(query, model string) (string, error) {
	if len(o.Token) == 0 {
		o.Token = os.Getenv("OPEN_ROUTER_API_KEY")
	}
	client := openrouter.NewClient(
		o.Token,
		openrouter.WithXTitle("Vulnerability Finding"),
		openrouter.WithHTTPReferer("https://openrouter.ai/api/v1"),
	)
	resp, err := client.CreateChatCompletion(
		context.Background(),
		openrouter.ChatCompletionRequest{
			Model: model,
			Messages: []openrouter.ChatCompletionMessage{
				{
					Role:    openrouter.ChatMessageRoleSystem,
					Content: openrouter.Content{Text: "You are a cybersecurity analyst."},
				},
				{
					Role:    openrouter.ChatMessageRoleUser,
					Content: openrouter.Content{Text: query},
				},
			},
			Transforms: []string{"middle-out"}, // Compress prompts that are > context size.
			//ResponseFormat: &openrouter.ChatCompletionResponseFormat{
			//	Type: openrouter.ChatCompletionResponseFormatTypeJSONSchema,
			//	JSONSchema: &openrouter.ChatCompletionResponseFormatJSONSchema{
			//		Name:   "poc finder",
			//		Schema: o.schema(),
			//		Strict: true,
			//	},
			//},
		},
	)

	if err != nil {
		slog.Error("ChatCompletion", "err", err)
		return "", nil
	}

	return resp.Choices[0].Message.Content.Text, nil
}

func (o *OpenRouter) schema() json.Marshaler {
	var schema JSONFormat
	return &schema
}
