// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v5.28.0--rc1
// source: proto/vuln_pusher.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 漏洞信息
type VulnInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueKey    string   `protobuf:"bytes,1,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`
	Title        string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Description  string   `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Severity     string   `protobuf:"bytes,4,opt,name=severity,proto3" json:"severity,omitempty"`
	Cve          string   `protobuf:"bytes,5,opt,name=cve,proto3" json:"cve,omitempty"`
	Disclosure   string   `protobuf:"bytes,6,opt,name=disclosure,proto3" json:"disclosure,omitempty"`
	Solutions    string   `protobuf:"bytes,7,opt,name=solutions,proto3" json:"solutions,omitempty"`
	GithubSearch []string `protobuf:"bytes,8,rep,name=github_search,json=githubSearch,proto3" json:"github_search,omitempty"`
	References   []string `protobuf:"bytes,9,rep,name=references,proto3" json:"references,omitempty"`
	Tags         []string `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`
	From         string   `protobuf:"bytes,11,opt,name=from,proto3" json:"from,omitempty"`
	Reason       []string `protobuf:"bytes,12,rep,name=reason,proto3" json:"reason,omitempty"`
}

func (x *VulnInfo) Reset() {
	*x = VulnInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VulnInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VulnInfo) ProtoMessage() {}

func (x *VulnInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VulnInfo.ProtoReflect.Descriptor instead.
func (*VulnInfo) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{0}
}

func (x *VulnInfo) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *VulnInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VulnInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VulnInfo) GetSeverity() string {
	if x != nil {
		return x.Severity
	}
	return ""
}

func (x *VulnInfo) GetCve() string {
	if x != nil {
		return x.Cve
	}
	return ""
}

func (x *VulnInfo) GetDisclosure() string {
	if x != nil {
		return x.Disclosure
	}
	return ""
}

func (x *VulnInfo) GetSolutions() string {
	if x != nil {
		return x.Solutions
	}
	return ""
}

func (x *VulnInfo) GetGithubSearch() []string {
	if x != nil {
		return x.GithubSearch
	}
	return nil
}

func (x *VulnInfo) GetReferences() []string {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *VulnInfo) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VulnInfo) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *VulnInfo) GetReason() []string {
	if x != nil {
		return x.Reason
	}
	return nil
}

// 数据源信息
type Provider struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Link        string `protobuf:"bytes,3,opt,name=link,proto3" json:"link,omitempty"`
}

func (x *Provider) Reset() {
	*x = Provider{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Provider) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Provider) ProtoMessage() {}

func (x *Provider) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Provider.ProtoReflect.Descriptor instead.
func (*Provider) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{1}
}

func (x *Provider) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Provider) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Provider) GetLink() string {
	if x != nil {
		return x.Link
	}
	return ""
}

// 初始化消息
type InitialMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version        string      `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	VulnCount      int32       `protobuf:"varint,2,opt,name=vuln_count,json=vulnCount,proto3" json:"vuln_count,omitempty"`
	Interval       string      `protobuf:"bytes,3,opt,name=interval,proto3" json:"interval,omitempty"`
	Provider       []*Provider `protobuf:"bytes,4,rep,name=provider,proto3" json:"provider,omitempty"`
	FailedProvider []*Provider `protobuf:"bytes,5,rep,name=failed_provider,json=failedProvider,proto3" json:"failed_provider,omitempty"`
}

func (x *InitialMessage) Reset() {
	*x = InitialMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitialMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitialMessage) ProtoMessage() {}

func (x *InitialMessage) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitialMessage.ProtoReflect.Descriptor instead.
func (*InitialMessage) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{2}
}

func (x *InitialMessage) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *InitialMessage) GetVulnCount() int32 {
	if x != nil {
		return x.VulnCount
	}
	return 0
}

func (x *InitialMessage) GetInterval() string {
	if x != nil {
		return x.Interval
	}
	return ""
}

func (x *InitialMessage) GetProvider() []*Provider {
	if x != nil {
		return x.Provider
	}
	return nil
}

func (x *InitialMessage) GetFailedProvider() []*Provider {
	if x != nil {
		return x.FailedProvider
	}
	return nil
}

// 推送漏洞请求
type PushVulnRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VulnInfo  *VulnInfo `protobuf:"bytes,1,opt,name=vuln_info,json=vulnInfo,proto3" json:"vuln_info,omitempty"`
	Timestamp int64     `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *PushVulnRequest) Reset() {
	*x = PushVulnRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushVulnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushVulnRequest) ProtoMessage() {}

func (x *PushVulnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushVulnRequest.ProtoReflect.Descriptor instead.
func (*PushVulnRequest) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{3}
}

func (x *PushVulnRequest) GetVulnInfo() *VulnInfo {
	if x != nil {
		return x.VulnInfo
	}
	return nil
}

func (x *PushVulnRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// 推送漏洞响应
type PushVulnResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PushVulnResponse) Reset() {
	*x = PushVulnResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushVulnResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushVulnResponse) ProtoMessage() {}

func (x *PushVulnResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushVulnResponse.ProtoReflect.Descriptor instead.
func (*PushVulnResponse) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{4}
}

func (x *PushVulnResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PushVulnResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 推送初始化请求
type PushInitialRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InitialMessage *InitialMessage `protobuf:"bytes,1,opt,name=initial_message,json=initialMessage,proto3" json:"initial_message,omitempty"`
	Timestamp      int64           `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *PushInitialRequest) Reset() {
	*x = PushInitialRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushInitialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushInitialRequest) ProtoMessage() {}

func (x *PushInitialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushInitialRequest.ProtoReflect.Descriptor instead.
func (*PushInitialRequest) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{5}
}

func (x *PushInitialRequest) GetInitialMessage() *InitialMessage {
	if x != nil {
		return x.InitialMessage
	}
	return nil
}

func (x *PushInitialRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// 推送初始化响应
type PushInitialResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PushInitialResponse) Reset() {
	*x = PushInitialResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushInitialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushInitialResponse) ProtoMessage() {}

func (x *PushInitialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushInitialResponse.ProtoReflect.Descriptor instead.
func (*PushInitialResponse) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{6}
}

func (x *PushInitialResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PushInitialResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 推送文本请求
type PushTextRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message   string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp int64  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *PushTextRequest) Reset() {
	*x = PushTextRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushTextRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTextRequest) ProtoMessage() {}

func (x *PushTextRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTextRequest.ProtoReflect.Descriptor instead.
func (*PushTextRequest) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{7}
}

func (x *PushTextRequest) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PushTextRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// 推送文本响应
type PushTextResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *PushTextResponse) Reset() {
	*x = PushTextResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_vuln_pusher_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushTextResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTextResponse) ProtoMessage() {}

func (x *PushTextResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_vuln_pusher_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTextResponse.ProtoReflect.Descriptor instead.
func (*PushTextResponse) Descriptor() ([]byte, []int) {
	return file_proto_vuln_pusher_proto_rawDescGZIP(), []int{8}
}

func (x *PushTextResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PushTextResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_proto_vuln_pusher_proto protoreflect.FileDescriptor

var file_proto_vuln_pusher_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75, 0x73,
	0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76, 0x75, 0x6c, 0x6e, 0x5f,
	0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x22, 0xd2, 0x02, 0x0a, 0x08, 0x56, 0x75, 0x6c, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65,
	0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65,
	0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x76, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x69, 0x73, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x69,
	0x73, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x6f, 0x6c,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x5f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0c, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x55, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x69,
	0x6e, 0x6b, 0x22, 0xd8, 0x01, 0x0a, 0x0e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x76, 0x75, 0x6c, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76,
	0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69,
	0x64, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x0a,
	0x0f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x22, 0x63, 0x0a,
	0x0f, 0x50, 0x75, 0x73, 0x68, 0x56, 0x75, 0x6c, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x09, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65,
	0x72, 0x2e, 0x56, 0x75, 0x6c, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x76, 0x75, 0x6c, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x22, 0x46, 0x0a, 0x10, 0x50, 0x75, 0x73, 0x68, 0x56, 0x75, 0x6c, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x78, 0x0a, 0x12, 0x50, 0x75,
	0x73, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x44, 0x0a, 0x0f, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x75, 0x6c, 0x6e,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x22, 0x49, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x49, 0x0a, 0x0f, 0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x46, 0x0a, 0x10, 0x50, 0x75,
	0x73, 0x68, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x32, 0xf0, 0x01, 0x0a, 0x0a, 0x56, 0x75, 0x6c, 0x6e, 0x50, 0x75, 0x73, 0x68, 0x65,
	0x72, 0x12, 0x47, 0x0a, 0x08, 0x50, 0x75, 0x73, 0x68, 0x56, 0x75, 0x6c, 0x6e, 0x12, 0x1c, 0x2e,
	0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68,
	0x56, 0x75, 0x6c, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x76, 0x75,
	0x6c, 0x6e, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x56, 0x75,
	0x6c, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x50, 0x75,
	0x73, 0x68, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x1f, 0x2e, 0x76, 0x75, 0x6c, 0x6e,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x76, 0x75, 0x6c,
	0x6e, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x08,
	0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1c, 0x2e, 0x76, 0x75, 0x6c, 0x6e, 0x5f,
	0x70, 0x75, 0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x76, 0x75, 0x6c, 0x6e, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x12, 0x5a, 0x10, 0x70, 0x6f, 0x63, 0x2d, 0x66, 0x69, 0x6e,
	0x64, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_proto_vuln_pusher_proto_rawDescOnce sync.Once
	file_proto_vuln_pusher_proto_rawDescData = file_proto_vuln_pusher_proto_rawDesc
)

func file_proto_vuln_pusher_proto_rawDescGZIP() []byte {
	file_proto_vuln_pusher_proto_rawDescOnce.Do(func() {
		file_proto_vuln_pusher_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_vuln_pusher_proto_rawDescData)
	})
	return file_proto_vuln_pusher_proto_rawDescData
}

var file_proto_vuln_pusher_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proto_vuln_pusher_proto_goTypes = []interface{}{
	(*VulnInfo)(nil),            // 0: vuln_pusher.VulnInfo
	(*Provider)(nil),            // 1: vuln_pusher.Provider
	(*InitialMessage)(nil),      // 2: vuln_pusher.InitialMessage
	(*PushVulnRequest)(nil),     // 3: vuln_pusher.PushVulnRequest
	(*PushVulnResponse)(nil),    // 4: vuln_pusher.PushVulnResponse
	(*PushInitialRequest)(nil),  // 5: vuln_pusher.PushInitialRequest
	(*PushInitialResponse)(nil), // 6: vuln_pusher.PushInitialResponse
	(*PushTextRequest)(nil),     // 7: vuln_pusher.PushTextRequest
	(*PushTextResponse)(nil),    // 8: vuln_pusher.PushTextResponse
}
var file_proto_vuln_pusher_proto_depIdxs = []int32{
	1, // 0: vuln_pusher.InitialMessage.provider:type_name -> vuln_pusher.Provider
	1, // 1: vuln_pusher.InitialMessage.failed_provider:type_name -> vuln_pusher.Provider
	0, // 2: vuln_pusher.PushVulnRequest.vuln_info:type_name -> vuln_pusher.VulnInfo
	2, // 3: vuln_pusher.PushInitialRequest.initial_message:type_name -> vuln_pusher.InitialMessage
	3, // 4: vuln_pusher.VulnPusher.PushVuln:input_type -> vuln_pusher.PushVulnRequest
	5, // 5: vuln_pusher.VulnPusher.PushInitial:input_type -> vuln_pusher.PushInitialRequest
	7, // 6: vuln_pusher.VulnPusher.PushText:input_type -> vuln_pusher.PushTextRequest
	4, // 7: vuln_pusher.VulnPusher.PushVuln:output_type -> vuln_pusher.PushVulnResponse
	6, // 8: vuln_pusher.VulnPusher.PushInitial:output_type -> vuln_pusher.PushInitialResponse
	8, // 9: vuln_pusher.VulnPusher.PushText:output_type -> vuln_pusher.PushTextResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_vuln_pusher_proto_init() }
func file_proto_vuln_pusher_proto_init() {
	if File_proto_vuln_pusher_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_vuln_pusher_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VulnInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Provider); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitialMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushVulnRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushVulnResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushInitialRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushInitialResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushTextRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_vuln_pusher_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushTextResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_vuln_pusher_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_vuln_pusher_proto_goTypes,
		DependencyIndexes: file_proto_vuln_pusher_proto_depIdxs,
		MessageInfos:      file_proto_vuln_pusher_proto_msgTypes,
	}.Build()
	File_proto_vuln_pusher_proto = out.File
	file_proto_vuln_pusher_proto_rawDesc = nil
	file_proto_vuln_pusher_proto_goTypes = nil
	file_proto_vuln_pusher_proto_depIdxs = nil
}
