// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v5.28.0--rc1
// source: proto/vuln_pusher.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	VulnPusher_PushVuln_FullMethodName    = "/vuln_pusher.VulnPusher/PushVuln"
	VulnPusher_PushInitial_FullMethodName = "/vuln_pusher.VulnPusher/PushInitial"
	VulnPusher_PushText_FullMethodName    = "/vuln_pusher.VulnPusher/PushText"
)

// VulnPusherClient is the client API for VulnPusher service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 漏洞推送服务
type VulnPusherClient interface {
	// 推送漏洞信息
	PushVuln(ctx context.Context, in *PushVulnRequest, opts ...grpc.CallOption) (*PushVulnResponse, error)
	// 推送初始化消息
	PushInitial(ctx context.Context, in *PushInitialRequest, opts ...grpc.CallOption) (*PushInitialResponse, error)
	// 推送文本消息
	PushText(ctx context.Context, in *PushTextRequest, opts ...grpc.CallOption) (*PushTextResponse, error)
}

type vulnPusherClient struct {
	cc grpc.ClientConnInterface
}

func NewVulnPusherClient(cc grpc.ClientConnInterface) VulnPusherClient {
	return &vulnPusherClient{cc}
}

func (c *vulnPusherClient) PushVuln(ctx context.Context, in *PushVulnRequest, opts ...grpc.CallOption) (*PushVulnResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushVulnResponse)
	err := c.cc.Invoke(ctx, VulnPusher_PushVuln_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vulnPusherClient) PushInitial(ctx context.Context, in *PushInitialRequest, opts ...grpc.CallOption) (*PushInitialResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushInitialResponse)
	err := c.cc.Invoke(ctx, VulnPusher_PushInitial_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vulnPusherClient) PushText(ctx context.Context, in *PushTextRequest, opts ...grpc.CallOption) (*PushTextResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushTextResponse)
	err := c.cc.Invoke(ctx, VulnPusher_PushText_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VulnPusherServer is the server API for VulnPusher service.
// All implementations must embed UnimplementedVulnPusherServer
// for forward compatibility
//
// 漏洞推送服务
type VulnPusherServer interface {
	// 推送漏洞信息
	PushVuln(context.Context, *PushVulnRequest) (*PushVulnResponse, error)
	// 推送初始化消息
	PushInitial(context.Context, *PushInitialRequest) (*PushInitialResponse, error)
	// 推送文本消息
	PushText(context.Context, *PushTextRequest) (*PushTextResponse, error)
	mustEmbedUnimplementedVulnPusherServer()
}

// UnimplementedVulnPusherServer must be embedded to have forward compatible implementations.
type UnimplementedVulnPusherServer struct {
}

func (UnimplementedVulnPusherServer) PushVuln(context.Context, *PushVulnRequest) (*PushVulnResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushVuln not implemented")
}
func (UnimplementedVulnPusherServer) PushInitial(context.Context, *PushInitialRequest) (*PushInitialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushInitial not implemented")
}
func (UnimplementedVulnPusherServer) PushText(context.Context, *PushTextRequest) (*PushTextResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushText not implemented")
}
func (UnimplementedVulnPusherServer) mustEmbedUnimplementedVulnPusherServer() {}

// UnsafeVulnPusherServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VulnPusherServer will
// result in compilation errors.
type UnsafeVulnPusherServer interface {
	mustEmbedUnimplementedVulnPusherServer()
}

func RegisterVulnPusherServer(s grpc.ServiceRegistrar, srv VulnPusherServer) {
	s.RegisterService(&VulnPusher_ServiceDesc, srv)
}

func _VulnPusher_PushVuln_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushVulnRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VulnPusherServer).PushVuln(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VulnPusher_PushVuln_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VulnPusherServer).PushVuln(ctx, req.(*PushVulnRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VulnPusher_PushInitial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushInitialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VulnPusherServer).PushInitial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VulnPusher_PushInitial_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VulnPusherServer).PushInitial(ctx, req.(*PushInitialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VulnPusher_PushText_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushTextRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VulnPusherServer).PushText(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VulnPusher_PushText_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VulnPusherServer).PushText(ctx, req.(*PushTextRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VulnPusher_ServiceDesc is the grpc.ServiceDesc for VulnPusher service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VulnPusher_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vuln_pusher.VulnPusher",
	HandlerType: (*VulnPusherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushVuln",
			Handler:    _VulnPusher_PushVuln_Handler,
		},
		{
			MethodName: "PushInitial",
			Handler:    _VulnPusher_PushInitial_Handler,
		},
		{
			MethodName: "PushText",
			Handler:    _VulnPusher_PushText_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/vuln_pusher.proto",
}
