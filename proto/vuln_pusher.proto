syntax = "proto3";

package vuln_pusher;

option go_package = "poc-finder/proto";

// 漏洞推送服务
service VulnPusher {
  // 推送漏洞信息
  rpc PushVuln(PushVulnRequest) returns (PushVulnResponse);
  
  // 推送初始化消息
  rpc PushInitial(PushInitialRequest) returns (PushInitialResponse);
  
  // 推送文本消息
  rpc PushText(PushTextRequest) returns (PushTextResponse);
}

// 漏洞信息
message VulnInfo {
  string unique_key = 1;
  string title = 2;
  string description = 3;
  string severity = 4;
  string cve = 5;
  string disclosure = 6;
  string solutions = 7;
  repeated string github_search = 8;
  repeated string references = 9;
  repeated string tags = 10;
  string from = 11;
  repeated string reason = 12;
}

// 数据源信息
message Provider {
  string name = 1;
  string display_name = 2;
  string link = 3;
}

// 初始化消息
message InitialMessage {
  string version = 1;
  int32 vuln_count = 2;
  string interval = 3;
  repeated Provider provider = 4;
  repeated Provider failed_provider = 5;
}

// 推送漏洞请求
message PushVulnRequest {
  VulnInfo vuln_info = 1;
  int64 timestamp = 2;
}

// 推送漏洞响应
message PushVulnResponse {
  bool success = 1;
  string message = 2;
}

// 推送初始化请求
message PushInitialRequest {
  InitialMessage initial_message = 1;
  int64 timestamp = 2;
}

// 推送初始化响应
message PushInitialResponse {
  bool success = 1;
  string message = 2;
}

// 推送文本请求
message PushTextRequest {
  string message = 1;
  int64 timestamp = 2;
}

// 推送文本响应
message PushTextResponse {
  bool success = 1;
  string message = 2;
}
