package model

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"

	"poc-finder/pkg/results"
)

type Vulnerability struct {
	ID          uuid.UUID `gorm:"type:uuid;primaryKey;index"`
	VulnID      string    `gorm:"column:vuln_id;uniqueIndex:idx_vuln,priority:1"`
	VulnName    string    `gorm:"column:vuln_name;uniqueIndex:idx_vuln,priority:2"`
	Severity    string    `gorm:"column:severity"`
	Description string
	Tag         string    `gorm:"column:tag"`
	HasPOCOrEXP bool      `gorm:"column:has_poc_or_exp;default:false"`
	ClosureTime time.Time `gorm:"column:closure_time;type:date"` // 该漏洞首次披露的日期
	CreatedAt   time.Time `gorm:"autoCreateTime"`                // 系统记录这条漏洞信息的时间
	UpdatedAt   time.Time
	NextRetry   time.Time

	Records []Record `gorm:"foreignKey:VulnID,VulnName;references:VulnID,VulnName;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
}

type Record struct {
	ID uuid.UUID `gorm:"type:uuid;primaryKey;index"`

	VulnID     string `gorm:"column:vuln_id;uniqueIndex:idx_record,priority:1"`
	RepoName   string `gorm:"column:repository;not null;uniqueIndex:idx_record,priority:2"`  // 仓库名
	SourceType string `gorm:"column:source_type;not null;uniqueIndex:idx_record,priority:3"` // 数据来源
	VulnName   string `gorm:"column:vuln_name;not null"`

	RepoLink     string // 仓库链接
	Description  string
	HasPOC       bool
	POCFile      string
	POCLink      string
	Reason       string
	FirstChecked time.Time // 首次外部查找的时间
	LastChecked  time.Time // 最后一次外部查找的时间
}

func OpenDB(dsn string) (*gorm.DB, error) {
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {

		return nil, fmt.Errorf("连接数据库: %w", err)
	}
	// 自动建表
	if err = db.AutoMigrate(&Vulnerability{}, &Record{}); err != nil {
		return nil, fmt.Errorf("自动建表失败: %w", err)
	}
	return db, nil
}

// SaveRepoRecord 子表存储
func SaveRepoRecord(db *gorm.DB, v *Vulnerability, repo *results.RepoInfo, now time.Time) error {
	record := &Record{
		ID:           uuid.New(), // 如果新插入会用到
		VulnID:       v.VulnID,
		VulnName:     v.VulnName,
		RepoName:     repo.RepositoryName,
		RepoLink:     repo.RepoLink,
		SourceType:   repo.SourceType,
		Description:  repo.Description,
		HasPOC:       repo.HasPOC,
		POCFile:      repo.FileName,
		POCLink:      repo.FileLink,
		Reason:       repo.Reason,
		FirstChecked: now,
		LastChecked:  now,
	}

	v.Records = append(v.Records, *record)

	// Upsert：插入或更新子表
	return db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "vuln_id"},
			{Name: "repository"},
			{Name: "source_type"},
		},
		DoUpdates: clause.AssignmentColumns([]string{
			"repo_link",
			"description",
			"has_poc",
			"poc_file",
			"poc_link",
			"reason",
			"last_checked",
		}),
	}).Create(record).Error
}

// SaveOrUpdateVuln 根据 VulnName/VulnName 查找漏洞，
// 不存在则插入，存在则更新。
// fn 负责写 Record。
// 返回：最新 vuln 对象，是否新建，是否首次带上 POC，和 error。
func SaveOrUpdateVuln(db *gorm.DB, v *Vulnerability, repo *results.RepoInfo, nextRetry time.Duration,
	fn func(db *gorm.DB, v *Vulnerability, repo *results.RepoInfo, now time.Time) error) (_ *Vulnerability, _err error) {
	// 1. 参数校验
	if v.VulnID == "" && v.VulnName == "" {
		return nil, fmt.Errorf("vulnerability 必须带有 VulnName 或 VulnName 其一")
	}
	// 更新基础字段
	now := time.Now()
	v.ID = uuid.New()
	v.UpdatedAt = now
	v.NextRetry = now.Add(nextRetry)

	if v.HasPOCOrEXP {
		// 如果存在，则下次不需要查找
		v.NextRetry = time.Time{}
	}
	// 注意：CreatedAt 在 SQL 里只能在 INSERT 时生效，
	//       可以给一个默认值或使用 GORM 自动处理

	// 2. Upsert 主表
	if err := db.Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "vuln_id"}, {Name: "vuln_name"}},
		DoUpdates: clause.AssignmentColumns([]string{
			"description", "tag", "has_poc_or_exp",
			"closure_time", "updated_at", "next_retry",
		}),
	}).Omit("Records").Create(v).Error; err != nil {
		return nil, fmt.Errorf("upsert 漏洞 %s/%s 失败: %w", v.VulnID, v.VulnName, err)
	}

	// 3. 写子表
	if err := fn(db, v, repo, now); err != nil {
		return nil, err
	}

	return v, nil
}

// SearchVulnerability 通过指定漏洞ID、漏洞名称、仓库名称、数据来源查找漏洞数据
func SearchVulnerability(db *gorm.DB, vulID, vulName, repoName, sourceType string) ([]Vulnerability, error) {
	var records []Vulnerability
	// Preload 加载子表数据，填写参数为Vulnerability结构体的成员变量名称
	// Joins 这里左连接，支持Where关键字跨表筛选或查询特定字段
	query := db.Model(&Vulnerability{}).Preload("records").Joins("LEFT JOIN repo_records ON vulnerabilities.vuln_id = repo_records.vuln_id")
	if len(vulID) > 0 {
		query = query.Where("vulnerabilities.vuln_id = ?", vulID)
	}
	if len(vulName) > 0 {
		query = query.Where("vulnerabilities.vuln_name LIKE ?", "%"+vulName+"%")
	}
	if len(repoName) > 0 {
		query = query.Where("repo_records.repository = ?", repoName)
	}
	if len(sourceType) > 0 {
		query = query.Where("repo_records.source_type = ?", sourceType)
	}
	query = query.Order("vulnerabilities.created_at DESC")

	if err := query.Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

// SearchVulnRecordsBySlices 按一组 VulnID和VulnName 批量查询 Record
func SearchVulnRecordsBySlices(db *gorm.DB, vulIDs []string, vulnNames []string) ([]Vulnerability, error) {
	var recs []Vulnerability
	if err := db.
		Model(&Vulnerability{}).
		Preload("Records").
		Where("vuln_id IN ?", vulIDs).
		Or("vuln_name IN ?", vulnNames).
		Find(&recs).Error; err != nil {
		return nil, err
	}
	return recs, nil
}

func UpdateVulnerability(db *gorm.DB, vulnID, vulnName, description, tag string, hasPoC *bool, sourceType, repoName, pocFile, pocLink string) error {
	query := db.Model(&Vulnerability{})
	query = query.Preload("records")
	v := &Vulnerability{}
	repo := Record{}
	if len(vulnID) > 0 {
		v.VulnID = vulnID
	}
	if len(vulnName) > 0 {
		v.VulnName = vulnName
	}
	if len(description) > 0 {
		v.Description = description
	}
	if len(tag) > 0 {
		v.Tag = tag
	}
	if hasPoC != nil {
		v.HasPOCOrEXP = *hasPoC
		if *hasPoC {
			v.NextRetry = time.Time{}
		}
	}
	if len(sourceType) > 0 {
		repo.SourceType = sourceType
	}
	if len(repoName) > 0 {
		repo.RepoName = repoName
	}
	if len(pocFile) > 0 {
		repo.POCFile = pocFile
	}
	if len(pocLink) > 0 {
		repo.POCLink = pocLink
	}
	v.Records = append(v.Records, repo)
	v.UpdatedAt = time.Now()

	if err := query.Updates(v).Error; err != nil {
		return err
	}

	return nil
}
