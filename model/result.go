package model

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/xuri/excelize/v2"
)

func ExportResults(target, fileExt string, vulns []Vulnerability) error {
	var file string
	if len(filepath.Ext(target)) == 0 {
		file = fmt.Sprintf("%s.%s", target, fileExt)
	} else {
		file = target
	}
	switch fileExt {
	case "xlsx":
		if err := exportXLSX(file, vulns); err != nil {
			return err
		}
	case "xls":
		if err := exportXLSX(file, vulns); err != nil {
			return err
		}
	case "csv":
		if err := exportCSV(file, vulns); err != nil {
			return err
		}
	case "db":
		if err := exportVulnsToSQLite(file, vulns); err != nil {
			return err
		}
	}
	return nil
}

// exportCSV 导出 CSV
func exportCSV(path string, vulns []Vulnerability) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()

	w := csv.NewWriter(f)
	defer w.Flush()

	// 表头
	header := []string{
		"VulnName", "VulnName", "Description", "Tag", "HasPOCOrEXP",
		"ClosureTime", "CreatedAt", "UpdatedAt", "NextRetry",
	}
	if err := w.Write(header); err != nil {
		return err
	}

	// 每条 Vul 只写一行，不展开子表
	for _, v := range vulns {
		row := []string{
			v.VulnID,
			v.VulnName,
			v.Description,
			v.Tag,
			fmt.Sprintf("%t", v.HasPOCOrEXP),
			v.ClosureTime.Format(time.RFC3339),
			v.CreatedAt.Format(time.RFC3339),
			v.UpdatedAt.Format(time.RFC3339),
			v.NextRetry.Format(time.RFC3339),
		}
		if err := w.Write(row); err != nil {
			return err
		}
	}
	return nil
}

// exportXLSX 导出 XLSX/XLS
func exportXLSX(path string, vulns []Vulnerability) error {
	f := excelize.NewFile()
	sheet := f.GetSheetName(0)

	// 写表头
	headers := []string{"VulnName", "VulnName", "Description", "Tag", "HasPOCOrEXP", "ClosureTime", "CreatedAt", "UpdatedAt", "NextRetry"}
	for i, h := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		if err := f.SetCellValue(sheet, cell, h); err != nil {
			return err
		}
	}

	// 写数据
	for rowIdx, v := range vulns {
		values := []interface{}{
			v.VulnID,
			v.VulnName,
			v.Description,
			v.Tag,
			v.HasPOCOrEXP,
			v.ClosureTime.Format(time.RFC3339),
			v.CreatedAt.Format(time.RFC3339),
			v.UpdatedAt.Format(time.RFC3339),
			v.NextRetry.Format(time.RFC3339),
		}
		for colIdx, val := range values {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx+2)
			if err := f.SetCellValue(sheet, cell, val); err != nil {
				return err
			}
		}
	}

	// 保存
	if err := f.SaveAs(path); err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}
	return nil
}

// exportVulnsToSQLite 将漏洞数据写入 SQLite 文件
func exportVulnsToSQLite(path string, vulns []Vulnerability) error {
	// 如果文件已存在，可先删除
	if err := os.Remove(path); err != nil {
		return err
	}

	// 打开 SQLite 文件
	db, err := sql.Open("sqlite3", path)
	if err != nil {
		return err
	}
	defer db.Close()

	// 创建表
	_, err = db.Exec(`
    CREATE TABLE vulnerabilities (
        vul_id TEXT PRIMARY KEY,
        vul_name TEXT,
        description TEXT,
        tag TEXT,
        has_poc_or_exp BOOLEAN,
        closure_time DATETIME,
        created_at DATETIME,
        updated_at DATETIME,
        next_retry DATETIME
    );
    CREATE TABLE repo_records (
        id TEXT PRIMARY KEY,
        vul_id TEXT,
        repository TEXT,
        source_type TEXT,
        description TEXT,
        has_poc BOOLEAN,
        poc_file TEXT,
        poc_link TEXT,
        reason TEXT,
        first_checked DATETIME,
        last_checked DATETIME,
        FOREIGN KEY(vul_id) REFERENCES vulnerabilities(vul_id)
    );
    `)
	if err != nil {
		return err
	}

	// 插入数据
	vulnStmt, _ := db.Prepare(`
        INSERT INTO vulnerabilities(vul_id, vul_name, description, tag,
            has_poc_or_exp, closure_time, created_at, updated_at, next_retry)
        VALUES(?,?,?,?,?,?,?,?,?)
    `)
	repoStmt, _ := db.Prepare(`
        INSERT INTO repo_records(id, vul_id, repository, source_type,
            description, has_poc, poc_file, poc_link, reason, first_checked, last_checked)
        VALUES(?,?,?,?,?,?,?,?,?,?,?)
    `)

	for _, v := range vulns {
		_, err := vulnStmt.Exec(
			v.VulnID, v.VulnName, v.Description, v.Tag,
			v.HasPOCOrEXP, v.ClosureTime, v.CreatedAt, v.UpdatedAt, v.NextRetry,
		)
		if err != nil {
			return err
		}
		for _, r := range v.Records {
			if _, err := repoStmt.Exec(
				r.ID.String(), v.VulnID, r.RepoName, r.SourceType,
				r.Description, r.HasPOC, r.POCFile, r.POCLink, r.Reason,
				r.FirstChecked, r.LastChecked,
			); err != nil {
				return err
			}
		}
	}
	return nil
}
