# 任务级别限流控制分析与解决方案

## 🚨 当前问题分析

### 问题识别

当前 `runDaemon` 函数的限流控制存在以下问题：

1. **任务启动无限流**：`scan()` 函数直接调用 `handler.ProcessVulnTasks()`，没有任务级别的限流控制
2. **批量任务并发风险**：大量任务同时启动可能导致系统资源耗尽
3. **全局限流器使用不当**：限流器只在API调用时生效，无法控制任务启动频率

### 当前流程分析

```
runDaemon() 
├── 文件更新触发 → scan() → handler.ProcessVulnTasks() → 批量处理所有任务
├── 定时任务触发 → scan() → handler.ProcessVulnTasks() → 批量处理所有任务
└── 全局限流器只在每个任务的API调用时生效
```

**问题**：
- 如果有100个到期漏洞，会同时启动100个任务处理
- 每个任务内部虽然有API限流，但任务启动本身无限流
- 可能导致内存、CPU、网络连接数等资源耗尽

## 🔧 解决方案设计

### 方案1：任务队列 + 工作池模式

```go
type TaskScheduler struct {
    taskQueue    chan handler.VulnTask
    workerPool   int
    rateLimiter  *time.Ticker
    ctx          context.Context
    cfg          *config.Config
}

// 控制任务启动频率的调度器
func (ts *TaskScheduler) Start() {
    // 启动固定数量的工作协程
    for i := 0; i < ts.workerPool; i++ {
        go ts.worker(i)
    }
    
    // 任务分发协程（受限流控制）
    go ts.dispatcher()
}

func (ts *TaskScheduler) dispatcher() {
    for {
        select {
        case <-ts.rateLimiter.C:
            // 限流器允许时才处理下一个任务
            select {
            case task := <-ts.taskQueue:
                ts.processTask(task)
            default:
                // 队列为空，继续等待
            }
        case <-ts.ctx.Done():
            return
        }
    }
}
```

### 方案2：分批处理 + 间隔控制

```go
func scanWithRateLimit(ctx context.Context, cfg *config.Config, tasks []VulnTask, batchSize int, interval time.Duration) {
    handlerTasks := convertTasks(tasks)
    
    // 分批处理
    for i := 0; i < len(handlerTasks); i += batchSize {
        end := i + batchSize
        if end > len(handlerTasks) {
            end = len(handlerTasks)
        }
        
        batch := handlerTasks[i:end]
        slog.Info("处理任务批次", "batch", i/batchSize+1, "tasks", len(batch))
        
        // 处理当前批次
        handler.ProcessVulnTasks(ctx, cfg, batch)
        
        // 批次间隔等待（除了最后一批）
        if end < len(handlerTasks) {
            select {
            case <-time.After(interval):
            case <-ctx.Done():
                return
            }
        }
    }
}
```

### 方案3：智能任务调度器（推荐）

结合全局限流器和任务级别控制的综合方案：

```go
type SmartTaskScheduler struct {
    cfg              *config.Config
    maxConcurrent    int           // 最大并发任务数
    taskInterval     time.Duration // 任务启动间隔
    batchSize        int           // 批处理大小
    semaphore        chan struct{} // 并发控制信号量
    taskQueue        chan handler.VulnTask
    ctx              context.Context
}

func NewSmartTaskScheduler(cfg *config.Config, ctx context.Context) *SmartTaskScheduler {
    return &SmartTaskScheduler{
        cfg:           cfg,
        maxConcurrent: 5,  // 最多5个并发任务
        taskInterval:  2 * time.Second, // 每2秒启动一个新任务
        batchSize:     10, // 每批处理10个漏洞
        semaphore:     make(chan struct{}, 5),
        taskQueue:     make(chan handler.VulnTask, 1000),
        ctx:           ctx,
    }
}
```

## 🎯 推荐实现方案

采用 **方案2（分批处理）+ 方案3（智能调度）** 的组合：

### 1. 修改 scan 函数

```go
// scan 使用任务级别限流的扫描函数
func scan(ctx context.Context, cfg *config.Config, tasks []VulnTask) {
    if len(tasks) == 0 {
        return
    }
    
    // 使用智能任务调度器
    scheduler := NewTaskLevelScheduler(cfg, ctx)
    scheduler.ProcessTasks(tasks)
}

type TaskLevelScheduler struct {
    cfg          *config.Config
    ctx          context.Context
    batchSize    int
    batchInterval time.Duration
    maxConcurrent int
}

func NewTaskLevelScheduler(cfg *config.Config, ctx context.Context) *TaskLevelScheduler {
    return &TaskLevelScheduler{
        cfg:           cfg,
        ctx:           ctx,
        batchSize:     10,                // 每批10个任务
        batchInterval: 5 * time.Second,   // 批次间隔5秒
        maxConcurrent: 3,                 // 最多3个并发批次
    }
}

func (tls *TaskLevelScheduler) ProcessTasks(tasks []VulnTask) {
    handlerTasks := convertToHandlerTasks(tasks)
    
    slog.Info("开始任务级别限流处理", 
        "total_tasks", len(handlerTasks),
        "batch_size", tls.batchSize,
        "batch_interval", tls.batchInterval)
    
    // 分批处理
    batches := tls.createBatches(handlerTasks)
    
    // 使用信号量控制并发批次数
    semaphore := make(chan struct{}, tls.maxConcurrent)
    var wg sync.WaitGroup
    
    for i, batch := range batches {
        wg.Add(1)
        go func(batchIndex int, batchTasks []handler.VulnTask) {
            defer wg.Done()
            
            // 获取信号量
            semaphore <- struct{}{}
            defer func() { <-semaphore }()
            
            // 批次间隔控制
            if batchIndex > 0 {
                select {
                case <-time.After(time.Duration(batchIndex) * tls.batchInterval):
                case <-tls.ctx.Done():
                    return
                }
            }
            
            slog.Info("处理任务批次", 
                "batch", batchIndex+1, 
                "tasks", len(batchTasks))
            
            // 处理当前批次（使用全局限流器）
            handler.ProcessVulnTasks(tls.ctx, tls.cfg, batchTasks)
            
        }(i, batch)
    }
    
    wg.Wait()
    slog.Info("任务级别限流处理完成", "total_batches", len(batches))
}
```

### 2. 配置化的限流参数

```go
type TaskSchedulingConfig struct {
    BatchSize        int           `toml:"batch_size"`        // 批处理大小
    BatchInterval    time.Duration `toml:"batch_interval"`    // 批次间隔
    MaxConcurrent    int           `toml:"max_concurrent"`    // 最大并发批次
    TaskStartDelay   time.Duration `toml:"task_start_delay"`  // 任务启动延迟
}

// 在 Config 结构体中添加
type Config struct {
    // ... 现有字段 ...
    TaskScheduling TaskSchedulingConfig `toml:"task_scheduling"`
}
```

### 3. 兼容性保证

```go
// 保持原有接口兼容性
func scanLegacy(ctx context.Context, cfg *config.Config, tasks []VulnTask) {
    // 直接调用原有逻辑（用于gRPC和search命令）
    handlerTasks := convertToHandlerTasks(tasks)
    handler.ProcessVulnTasks(ctx, cfg, handlerTasks)
}

// 在不同场景使用不同的扫描函数
func runDaemon(cmd *cobra.Command, args []string) error {
    // ... 现有代码 ...
    
    // 文件更新触发 - 使用限流扫描
    scan(ctx, cfg, tasks)
    
    // 定时任务触发 - 使用限流扫描  
    scan(ctx, cfg, vulnTasks)
}

// gRPC模式 - 使用直接扫描（保持快速响应）
func (h *IntegratedVulnHandler) triggerPocSearch(...) {
    // 单个任务，直接处理
    vuln := handler.ProcessVulnTask(ctx, h.cfg, task)
}

// search命令 - 使用直接扫描（用户期望立即响应）
func runSearch(cmd *cobra.Command, args []string) error {
    // ... 现有代码 ...
    scanLegacy(ctx, cfg, tasks)
}
```

## 📊 方案对比

| 特性 | 当前实现 | 方案1(队列) | 方案2(分批) | 方案3(智能) | 推荐方案 |
|------|----------|-------------|-------------|-------------|----------|
| **任务启动控制** | ❌ 无 | ✅ 队列控制 | ✅ 分批控制 | ✅ 智能控制 | ✅ 分批+智能 |
| **资源使用控制** | ❌ 无限制 | ✅ 工作池 | ✅ 批次限制 | ✅ 信号量 | ✅ 多重控制 |
| **实现复杂度** | ✅ 简单 | ⚠️ 复杂 | ✅ 中等 | ⚠️ 复杂 | ✅ 中等 |
| **兼容性** | ✅ 完全 | ⚠️ 需修改 | ✅ 完全 | ⚠️ 需修改 | ✅ 完全 |
| **可配置性** | ❌ 无 | ✅ 高 | ✅ 中等 | ✅ 高 | ✅ 高 |

## ✅ 实施建议

1. **第一阶段**：实现分批处理机制，解决批量任务问题
2. **第二阶段**：添加配置化参数，支持不同场景调优
3. **第三阶段**：增加智能调度特性，如动态批次大小调整

这个方案既解决了任务级别限流问题，又保持了向后兼容性，是最佳的实施路径。
