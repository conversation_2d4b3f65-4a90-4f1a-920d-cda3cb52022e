package main

import (
	"context"
	_ "embed"
	"fmt"
	"log/slog"
	"net"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"poc-finder/config"
	"poc-finder/model"
	grpc2 "poc-finder/pkg/grpc"
	taskscheduler "poc-finder/pkg/scheduler"
	"poc-finder/pkg/tool"
	pb "poc-finder/proto"
)

var (
	cfgFile    string
	hasPoCFlag bool
)

// VulnTask 漏洞扫描任务
type VulnTask struct {
	Value string   // 漏洞ID或名称
	Type  string   // "ID" 或 "Name"
	Tags  []string // 漏洞标签，用于决定使用哪些处理器
}

var rootCmd = &cobra.Command{
	Use:   "poc-finder",
	Short: "持续查找指定 CVE",
}

var searchCmd = &cobra.Command{
	Use:   "search",
	Short: "手动查询指定漏洞(ID 或 名称)",
	RunE:  runSearch,
}

var modifyCmd = &cobra.Command{
	Use:   "modify",
	Short: "手动修改指定漏洞的漏洞属性",
	RunE:  runModify,
}

var daemonCmd = &cobra.Command{
	Use:   "daemon",
	Short: "后台模式，按数据库 next_retry 调度定期查询，同时启动gRPC服务",
	RunE:  runDaemon,
}

var grpcCmd = &cobra.Command{
	Use:   "grpc",
	Short: "启动gRPC服务，接收WatchVuln推送的漏洞信息",
	RunE:  runGRPC,
}

// todo: 漏洞价值分析

func init() {
	// 全局配置文件 flag
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径，默认使用内置")

	// search 子命令 flags
	searchCmd.PersistentFlags().String("vul-ID", "", "要查找的漏洞ID")
	searchCmd.PersistentFlags().String("vul-name", "", "漏洞名称")
	searchCmd.PersistentFlags().Bool("search-type", false, "查找模式，开启时在数据库中查找历史结果")
	searchCmd.PersistentFlags().Bool("export", false, "是否需要导出查询的记录，默认不导出")
	searchCmd.PersistentFlags().String("export-file-path", "", "导出文件路径")
	searchCmd.PersistentFlags().String("export-file-prefix", "db", "导出文件的后缀名，默认为DB，可选xlsx、xls、cvs (表格)")

	// modify 子命令 flags
	modifyCmd.PersistentFlags().String("vul-ID", "", "要修改的漏洞ID")
	modifyCmd.PersistentFlags().String("vul-name", "", "要修改的漏洞名称")
	modifyCmd.PersistentFlags().BoolVar(&hasPoCFlag, "set-has-poc", false, "手动修改数据库中指定漏洞是否存在公开POC/EXP数据")
	modifyCmd.PersistentFlags().String("set-source-type", "", "手动增加数据库中指定漏洞关联的查找数据源")
	modifyCmd.PersistentFlags().String("set-repo-name", "", "手动增加数据库中指定漏洞查找到的仓库名")
	modifyCmd.PersistentFlags().String("set-repo-poc-file", "", "手动修改数据库中指定漏洞查找到的仓库名中的POC文件名")
	modifyCmd.PersistentFlags().String("set-repo-poc-link", "", "手动修改数据库中指定漏洞查找到的仓库名中的POC文件链接")
	modifyCmd.PersistentFlags().String("set-repo-description", "", "手动修改仓库描述")
	modifyCmd.PersistentFlags().String("set-vuln-description", "", "手动修改漏洞描述")
	modifyCmd.PersistentFlags().String("set-vuln-tag", "", "手动修改漏洞标签")

	// 注册子命令
	rootCmd.AddCommand(searchCmd)
	rootCmd.AddCommand(daemonCmd)
	rootCmd.AddCommand(modifyCmd)
	rootCmd.AddCommand(grpcCmd)
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func runSearch(cmd *cobra.Command, args []string) error {
	// 1. 加载配置
	cfg, err := loadConfig()
	if err != nil {
		return err
	}

	// 2. 获取参数
	vulID, _ := cmd.Flags().GetString("vul-ID")
	vulName, _ := cmd.Flags().GetString("vul-name")
	vulPath := cfg.VulnFilePath
	// 获取列表漏洞数据，还需要实现指定漏洞ID或漏洞名称
	if vulID == "" && vulName == "" && vulPath == "" {
		return fmt.Errorf("必须指定 vul-id 或 vul-name 或 vul-file-path")
	}

	var vulns []string
	if len(vulPath) != 0 {
		data, err := os.ReadFile(vulPath)
		if err != nil {
			return err
		}
		vulns = strings.Split(string(data), "\n")
		if len(vulns) == 0 {
			return fmt.Errorf("漏洞列表为空")
		}
	}

	ctx, cancel := signal.NotifyContext(cfg.Context(), os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// 3. 根据 searchType 决定使用历史查找还是实时查找
	tasks := make([]VulnTask, 0)
	for _, v := range vulns {
		if tool.IsVulnID(v) {
			tasks = append(tasks, VulnTask{
				Value: v,
				Type:  "ID",
				Tags:  []string{}, // 从文件内容构建的任务没有标签信息
			})
		} else {
			tasks = append(tasks, VulnTask{
				Value: v,
				Type:  "Name",
				Tags:  []string{}, // 从文件内容构建的任务没有标签信息
			})
		}
	}
	searchType, _ := cmd.Flags().GetBool("search-type")
	var records []model.Vulnerability
	if !searchType {
		// search命令使用直接扫描，保持快速响应
		scanLegacy(ctx, cfg, tasks)
	} else {
		records, err = searchHistory(cfg, tasks)
		if err != nil {
			return err
		}
		for _, vuln := range records {
			slog.InfoContext(ctx, "历史记录", "vuln", vuln.VulnID, "has_poc", vuln.HasPOCOrEXP)
		}
	}

	// 是否需要导出
	export, _ := cmd.Flags().GetBool("export")
	if export {
		target, _ := cmd.Flags().GetString("export-file-path")
		if len(target) == 0 {
			return fmt.Errorf("导出名不能为空")
		}
		if len(records) == 0 {
			records, err = searchHistory(cfg, tasks)
		}
		if err != nil {
			return err
		}
		fileExt, _ := cmd.Flags().GetString("export-file-prefix")
		if err = model.ExportResults(target, fileExt, records); err != nil {
			return err
		}
		slog.InfoContext(ctx, "结果已导出到: ", fmt.Sprintf("%s.%s", target, fileExt))
	}

	return nil
}

func runDaemon(cmd *cobra.Command, args []string) error {
	// 1. 加载配置
	cfg, err := loadConfig()
	if err != nil {
		return err
	}
	db := cfg.DB

	// 2. 等待中断
	ctx, cancel := signal.NotifyContext(cfg.Context(), os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// 3. 确保在退出时停止全局限流器
	defer func() {
		slog.Info("正在停止全局限流器...")
		cfg.StopTickers()
		slog.Info("全局限流器已停止")
	}()

	// 4. 启动gRPC服务（如果未禁用）
	enableGrpc := cfg.Grpc.Enabled
	if enableGrpc {
		grpcPort := cfg.Grpc.Port
		grpcHost := cfg.Grpc.Host

		errCh := make(chan error, 1)
		readyCh := make(chan struct{}, 1)

		// 在goroutine中启动gRPC服务
		go func() {
			if err = startGRPCServiceWithReady(ctx, cfg, grpcHost, grpcPort, readyCh); err != nil {
				slog.Error("gRPC服务启动失败", "err", err)
				errCh <- err
			}
		}()

		// 等待gRPC服务启动或出错
		select {
		case err = <-errCh:
			return err
		case <-readyCh:
			slog.InfoContext(ctx, "gRPC服务已在后台启动", "address", fmt.Sprintf("%s:%s", grpcHost, grpcPort))
		case <-time.After(10 * time.Second):
			return fmt.Errorf("gRPC服务启动超时")
		}
	}

	// 5. 如果指定了文件路径，就启动文件监控，并做第一次过滤
	var fileUpdates <-chan taskscheduler.FileUpdate
	if cfg.VulnFilePath != "" {
		fileUpdates = taskscheduler.WatchFile(cfg, ctx, cfg.VulnFilePath)
		// 读取初始文件内容
		data, err := os.ReadFile(cfg.VulnFilePath)
		if err != nil {
			return err
		}

		buf := strings.ReplaceAll(string(data), "\r\n", "\n")
		lines := strings.Split(buf, "\n")
		ids, names := tool.SplitVulns(lines)
		scan(ctx, cfg, buildTasksFromSlices(ids, names))
	}

	for {
		// 先算下次定时任务的定时器
		nextTime, err := taskscheduler.NextRunTime(db)
		if err != nil {
			slog.Error("NextRunTime 失败", "err", err)
			nextTime = time.Now().Add(time.Hour)
		}
		timer := time.NewTimer(time.Until(nextTime))

		select {
		// —— a) 文件更新优先处理 ——
		case upd, ok := <-fileUpdates:
			if !ok {
				// channel 关闭或 context done
				cancel()
				return nil
			}
			if upd.Err != nil {
				slog.ErrorContext(ctx, "文件监控出错", "err", upd.Err)
				continue
			}
			slog.InfoContext(ctx, "检测到 vuln-file 更新，触发扫描")
			buf := strings.ReplaceAll(string(upd.Content), "\r\n", "\n")
			lines := strings.Split(buf, "\n")
			ids, names := tool.SplitVulns(lines)
			filteredIDs, filteredNames, err := taskscheduler.Filter(db, ids, names)
			if err != nil {
				slog.ErrorContext(ctx, "Filter 失败", "err", err)
				continue
			}
			tasks := buildTasksFromSlices(filteredIDs, filteredNames)
			scan(ctx, cfg, tasks)
		// —— b) 到期漏洞定时任务 ——
		case <-timer.C:
			// 拉取一次所有到期的漏洞
			due, err := taskscheduler.FetchDueVulns(db)
			if err != nil {
				slog.Error("FetchDueVulns 失败", "err", err)
				continue
			}
			if len(due) == 0 {
				slog.Info("暂无到期漏洞，等待下一次")
				continue
			}
			// 准备 []vulns
			vulnTasks := make([]VulnTask, 0, len(due))
			for _, v := range due {
				task := VulnTask{
					Tags: strings.Split(v.Tag, ","),
				}
				if v.VulnID == "" {
					task.Value = v.VulnName
					task.Type = "Name"
				} else {
					task.Value = v.VulnID
					task.Type = "ID"
				}
				vulnTasks = append(vulnTasks, task)
			}
			slog.Info("定时到期漏洞触发扫描", "count", len(due))
			scan(ctx, cfg, vulnTasks)

		// —— c) 退出信号 ——
		case <-ctx.Done():
			slog.Info("守护进程退出")
			return nil
		}

		// 确保 timer 停掉，避免泄漏
		if !timer.Stop() {
			<-timer.C
		}
	}
}

func runModify(cmd *cobra.Command, args []string) error {
	cfg, err := loadConfig()
	if err != nil {
		return err
	}
	vulnID, _ := cmd.Flags().GetString("vul-ID")
	vulnName, _ := cmd.Flags().GetString("vul-name")
	if len(vulnName) == 0 && len(vulnID) == 0 {
		return fmt.Errorf("需要输入漏洞ID或漏洞名称")
	}
	if err = cfg.Load(cfgFile); err != nil {
		return err
	}

	sourceType, _ := cmd.Flags().GetString("set-source-type")
	repoName, _ := cmd.Flags().GetString("set-repo-name")
	pocFile, _ := cmd.Flags().GetString("set-repo-poc-file")
	pocLink, _ := cmd.Flags().GetString("set-repo-poc-link")
	description, _ := cmd.Flags().GetString("set-vuln-description")
	tag, _ := cmd.Flags().GetString("set-vuln-tag")
	var hasPoC *bool
	if cmd.Flags().Changed("set-has-poc") {
		hasPoC = &hasPoCFlag
	}

	if err = model.UpdateVulnerability(cfg.DB, vulnID, vulnName, description, tag, hasPoC, sourceType, repoName, pocFile, pocLink); err != nil {
		return err
	}

	return nil
}

// startGRPCServiceWithReady 启动gRPC服务并通知就绪状态
func startGRPCServiceWithReady(ctx context.Context, cfg *config.Config, host, port string, readyCh chan<- struct{}) error {
	// 1. 创建漏洞处理器
	vulnHandler := grpc2.NewIntegratedVulnHandler(cfg)

	// 2. 创建gRPC服务器
	grpcServer := grpc.NewServer()

	// 3. 注册服务
	vulnPusherServer := grpc2.NewVulnPusherServer(vulnHandler)
	pb.RegisterVulnPusherServer(grpcServer, vulnPusherServer)

	// 4. 启用反射（便于调试）
	reflection.Register(grpcServer)

	// 5. 监听端口
	address := fmt.Sprintf("%s:%s", host, port)
	listener, err := net.Listen("tcp", address)
	if err != nil {
		return fmt.Errorf("监听端口失败: %w", err)
	}

	slog.Info("POC-Finder gRPC服务启动", "address", address)
	slog.Info("等待WatchVuln推送漏洞数据...")

	// 6. 设置优雅关闭
	go func() {
		<-ctx.Done()
		slog.Info("正在关闭gRPC服务...")
		grpcServer.GracefulStop()
		slog.Info("gRPC服务已关闭")
	}()

	// 7. 通知服务已准备好
	if readyCh != nil {
		close(readyCh)
	}

	// 8. 启动服务（阻塞调用）
	if err = grpcServer.Serve(listener); err != nil {
		return fmt.Errorf("启动gRPC服务失败: %w", err)
	}

	return nil
}

// startGRPCService 启动gRPC服务的通用函数
func startGRPCService(ctx context.Context, cfg *config.Config, host, port string) error {
	return startGRPCServiceWithReady(ctx, cfg, host, port, nil)
}

func runGRPC(cmd *cobra.Command, args []string) error {
	// 1. 加载配置
	cfg, err := loadConfig()
	if err != nil {
		return err
	}

	// 2. 获取参数
	host := cfg.Grpc.Host
	port := cfg.Grpc.Port

	// 3. 设置上下文
	ctx, cancel := signal.NotifyContext(cfg.Context(), os.Interrupt, syscall.SIGTERM)
	defer cancel()

	// 4. 确保在退出时停止全局限流器
	defer func() {
		slog.Info("正在停止全局限流器...")
		cfg.StopTickers()
		slog.Info("全局限流器已停止")
	}()

	// 5. 启动gRPC服务
	return startGRPCService(ctx, cfg, host, port)
}

// scan 使用任务级别限流的扫描函数（用于daemon模式）
func scan(ctx context.Context, cfg *config.Config, tasks []VulnTask) {
	if len(tasks) == 0 {
		return
	}

	// 转换为scheduler包的VulnTask格式
	schedulerTasks := make([]taskscheduler.VulnTask, len(tasks))
	for i, task := range tasks {
		schedulerTasks[i] = taskscheduler.VulnTask{
			Value: task.Value,
			Type:  task.Type,
			Tags:  task.Tags,
		}
	}

	// 使用任务级别调度器
	taskLevelScheduler := taskscheduler.NewTaskLevelScheduler(cfg, ctx)
	taskLevelScheduler.ProcessTasks(schedulerTasks)
}

// scanLegacy 保持向后兼容的直接扫描函数（用于gRPC和search命令）
func scanLegacy(ctx context.Context, cfg *config.Config, tasks []VulnTask) {
	if len(tasks) == 0 {
		return
	}

	// 转换为scheduler包的VulnTask格式
	schedulerTasks := make([]taskscheduler.VulnTask, len(tasks))
	for i, task := range tasks {
		schedulerTasks[i] = taskscheduler.VulnTask{
			Value: task.Value,
			Type:  task.Type,
			Tags:  task.Tags,
		}
	}

	// 使用任务级别调度器的直接处理模式（无任务级别限流）
	taskLevelScheduler := taskscheduler.NewTaskLevelScheduler(cfg, ctx)
	taskLevelScheduler.ProcessTasksDirect(schedulerTasks)
}

// buildTasksFromMap 从map构建任务列表
func buildTasksFromSlices(ids, names []string) []VulnTask {
	var tasks []VulnTask

	// 处理漏洞ID
	for _, id := range ids {
		tasks = append(tasks, VulnTask{
			Value: id,
			Type:  "ID",
			Tags:  []string{}, // 从数组构建的任务没有标签信息
		})
	}

	// 处理漏洞名称
	for _, name := range names {
		tasks = append(tasks, VulnTask{
			Value: name,
			Type:  "Name",
			Tags:  []string{}, // 从数组构建的任务没有标签信息
		})
	}

	return tasks
}

func loadConfig() (*config.Config, error) {
	// 解析并热加载配置
	cfg := config.NewConfig()
	if err := cfg.Load(cfgFile); err != nil {
		return nil, fmt.Errorf("配置加载失败: %w", err)
	}
	cfg.WatchConfig()

	return cfg, nil
}

// searchHistory 从历史数据库中查询 Record
// 传入三种可选定位：vulID, vulName, 或者 vulnList（多个 VulnName）
// 优先级：vulID > vulName > vulnList
func searchHistory(cfg *config.Config, tasks []VulnTask) ([]model.Vulnerability, error) {
	db := cfg.DB
	// 1. 构造要查询的 VulnName 列表
	var (
		ids   []string
		names []string
	)
	if len(tasks) == 0 {
		return nil, fmt.Errorf("必须指定 vulID 或 vulName 或 vulnList")
	}

	// 直接使用一批 VulnName
	for _, task := range tasks {
		if task.Type == "ID" {
			ids = append(ids, task.Value)
		} else {
			names = append(names, task.Value)
		}
	}

	if len(ids) == 0 {
		// 名称查不到任何 VulnName
		return nil, nil
	}

	// 2. 批量查询所有 Record
	//    这里一次性拿对应 VulnName 的所有子表记录
	vulns, err := model.SearchVulnRecordsBySlices(db, ids, names)
	if err != nil {
		return nil, fmt.Errorf("批量查询 VulnRecord 失败: %w", err)
	}
	return vulns, nil
}
