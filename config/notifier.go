package config

import (
	"fmt"

	"poc-finder/model"
	"poc-finder/pkg/robot"
)

type Notifier interface {
	NotifyVulnerability(v *model.Vulnerability) error
}

func (cfg *Config) NewNotifier() (Notifier, error) {
	hookCfg := cfg.Webhook
	switch hookCfg.SelectedHook {
	case "DingTalk":
		return robot.NewDingTalkNotifier(hookCfg.AccessToken, hookCfg.Secret), nil
	}
	return nil, fmt.Errorf("invalid hook selected")
}
