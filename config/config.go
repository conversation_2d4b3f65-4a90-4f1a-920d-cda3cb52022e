package config

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/go-resty/resty/v2"
	"github.com/radovskyb/watcher"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

//goland:noinspection SpellCheckingInspection
type Config struct {
	Client         ClientConfig         `mapstructure:"client" toml:"client"`
	Database       DatabaseConfig       `mapstructure:"database" toml:"database"`
	Source         Source               `mapstructure:"source" toml:"source"`
	Model          ModelConfig          `mapstructure:"model" toml:"model"`
	Webhook        WebhookConfig        `mapstructure:"webhook" toml:"webhook"`
	Grpc           GrpcConfig           `mapstructure:"grpc" toml:"grpc"`
	TaskScheduling TaskSchedulingConfig `mapstructure:"task_scheduling" toml:"task_scheduling"`
	VulnFilePath   string               `mapstructure:"vuln-file-path" toml:"vuln-file-path"`

	LLM          LLM              `toml:"-"`
	Notifier     Notifier         `toml:"-"`
	ctx          context.Context  `toml:"-"`
	resty        *resty.Client    `toml:"-"`
	viper        *viper.Viper     `toml:"-"`
	DB           *gorm.DB         `toml:"-"`
	GithubClient *resty.Client    `toml:"-"`
	NucleiClient *resty.Client    `toml:"-"`
	CVEClient    *resty.Client    `toml:"-"`
	Watcher      *watcher.Watcher `toml:"-"`

	// 全局限流器 - 确保所有模块共享相同的限流控制
	NucleiTicker *time.Ticker `toml:"-"`
	GithubTicker *time.Ticker `toml:"-"`
	tickerMutex  sync.RWMutex `toml:"-"` // 保护限流器的并发访问

	// 配置热重载相关
	reloadMutex sync.RWMutex    `toml:"-"` // 保护配置重载过程
	lastReload  time.Time       `toml:"-"` // 最后重载时间
	debouncer   *SmartDebouncer `toml:"-"` // 智能防抖器
}

type GrpcConfig struct {
	Enabled    bool   `mapstructure:"enabled" toml:"enabled"`
	Host       string `mapstructure:"host" toml:"host"`
	Port       string `mapstructure:"port" toml:"port"`
	Reflection bool   `mapstructure:"reflection" toml:"reflection"`
}

type WebhookConfig struct {
	SelectedHook string `mapstructure:"selected-hook" toml:"selected-hook"`
	AccessToken  string `mapstructure:"access-token" toml:"access-token"`
	Secret       string `mapstructure:"secret" toml:"secret"`
}

type DatabaseConfig struct {
	Type     string `mapstructure:"type" toml:"type"`
	Host     string `mapstructure:"host" toml:"host"`
	Port     string `mapstructure:"port" toml:"port"`
	DBName   string `mapstructure:"dbname" toml:"dbname"`
	Username string `mapstructure:"username" toml:"username"`
	Password string `mapstructure:"password" toml:"password"`
	Charset  string `mapstructure:"charset" toml:"charset"`
	SSLMode  string `mapstructure:"sslmode" toml:"sslmode"`
	Timezone string `mapstructure:"timezone" toml:"timezone"`
}

type ClientConfig struct {
	Timeout     string `mapstructure:"timeout" toml:"timeout"`
	RetryCount  int    `mapstructure:"retry-count" toml:"retry-count"`
	NextRetry   int    `mapstructure:"next-retry" toml:"next-retry"`
	UserAgent   `mapstructure:"UA" toml:"UA"`
	ProxyConfig `mapstructure:"proxy" toml:"proxy"`
}

type ProxyConfig struct {
	Address string `mapstructure:"address" toml:"address"`
	Enabled bool   `mapstructure:"enabled" toml:"enabled"`
}

type UserAgent struct {
	RandomEnabled bool   `mapstructure:"random" toml:"random"`
	UserAgent     string `mapstructure:"user-agent" toml:"user-agent"`
}

type Source struct {
	// 下次查找的时间间隔
	NextRetry time.Duration `mapstructure:"next-retry" toml:"next-retry"`
	// 顶层的 github-bearer 字段
	GitHubBearer string `mapstructure:"github-bearer" toml:"github-bearer"`
	// 子块 [source.github]
	GitHub SourceDetail `mapstructure:"github" toml:"github"`
	// 子块 [source.cve]
	CVE SourceDetail `mapstructure:"cve" toml:"cve"`
	// 子块 [source.nuclei]
	Nuclei SourceDetail `mapstructure:"nuclei" toml:"nuclei"`
}

// SourceDetail 对应各源的字段
type SourceDetail struct {
	Enabled     bool `mapstructure:"enabled" toml:"enabled"`
	Concurrency int  `mapstructure:"concurrency" toml:"concurrency"`
	LimitNum    int  `mapstructure:"limit-num" toml:"limit-num"`
}

type ModelConfig struct {
	SelectedModel string         `mapstructure:"selected-model" toml:"selected-model"`
	Concurrency   int            `mapstructure:"concurrency" toml:"concurrency"`
	OpenAI        ProviderConfig `mapstructure:"openai" toml:"openai"`
	OpenRouter    ProviderConfig `mapstructure:"openRouter" toml:"openRouter"`
	WenXin        ProviderConfig `mapstructure:"wenXin" toml:"wenXin"`
	Ollama        ProviderConfig `mapstructure:"ollama" toml:"ollama"`
}

type ProviderConfig struct {
	Token     string `mapstructure:"token" toml:"token"`
	Host      string `mapstructure:"host" toml:"host"`
	ModelName string `mapstructure:"model-name" toml:"model-name"`
}

func NewConfig() *Config {
	cfg := &Config{}
	cfg.resty = resty.New()
	cfg.ctx = context.Background()
	cfg.viper = viper.New()
	cfg.Watcher = watcher.New()

	return cfg
}

func (cfg *Config) Load(cfgPath string) error {
	if cfgPath != "" {
		// 如果提供了完整路径，分离目录和文件名
		dir := filepath.Dir(cfgPath)
		filename := filepath.Base(cfgPath)
		cfg.viper.AddConfigPath(dir)
		cfg.viper.SetConfigName(strings.TrimSuffix(filename, filepath.Ext(filename)))
	} else {
		// 默认配置
		cfg.viper.AddConfigPath(".")
		cfg.viper.SetConfigName("config")
	}
	cfg.viper.SetConfigType("toml")
	if err := cfg.viper.ReadInConfig(); err != nil {
		return err
	}
	if err := cfg.viper.Unmarshal(cfg); err != nil {
		return err
	}
	if cfg.Model.SelectedModel == "ollama" && cfg.Model.Ollama.Host != "" {
		if err := os.Setenv("OLLAMA_HOST", cfg.Model.Ollama.Host); err != nil {
			return err
		}
	}

	llm, err := NewLLM(cfg.Model)
	if err != nil {
		return err
	}
	cfg.LLM = llm

	if err = cfg.initDB(); err != nil {
		return err
	}

	cfg.initResty()

	notifier, err := cfg.NewNotifier()
	if err != nil {
		return err
	}
	cfg.Notifier = notifier

	// 初始化全局限流器
	if err = cfg.initGlobalTickers(); err != nil {
		return err
	}

	// 设置任务调度默认值
	cfg.TaskScheduling.SetDefaults()

	return nil
}

// WatchConfig 启动配置文件监控，实现智能防抖的热重载
func (cfg *Config) WatchConfig() {
	// 初始化智能防抖器
	cfg.initSmartDebouncer()

	cfg.viper.OnConfigChange(func(e fsnotify.Event) {
		cfg.smartReload(e)
	})
	cfg.viper.WatchConfig()
	slog.Info("🔍 配置文件监控已启动",
		"config_file", cfg.viper.ConfigFileUsed(),
		"debounce_short", cfg.debouncer.shortDelay,
		"debounce_long", cfg.debouncer.longDelay,
		"max_wait", cfg.debouncer.maxWaitTime)
}

func (cfg *Config) Context() context.Context {
	return cfg.ctx
}

// initGlobalTickers 初始化全局限流器
func (cfg *Config) initGlobalTickers() error {
	cfg.tickerMutex.Lock()
	defer cfg.tickerMutex.Unlock()

	// 计算限流间隔
	nucleiInterval := time.Minute / time.Duration(cfg.Source.Nuclei.LimitNum)
	githubInterval := time.Minute / time.Duration(cfg.Source.GitHub.LimitNum)

	// 创建全局限流器
	cfg.NucleiTicker = time.NewTicker(nucleiInterval)
	cfg.GithubTicker = time.NewTicker(githubInterval)

	return nil
}

// GetNucleiTicker 获取Nuclei限流器（线程安全）
func (cfg *Config) GetNucleiTicker() *time.Ticker {
	cfg.tickerMutex.RLock()
	defer cfg.tickerMutex.RUnlock()
	return cfg.NucleiTicker
}

// GetGithubTicker 获取GitHub限流器（线程安全）
func (cfg *Config) GetGithubTicker() *time.Ticker {
	cfg.tickerMutex.RLock()
	defer cfg.tickerMutex.RUnlock()
	return cfg.GithubTicker
}

// StopTickers 停止所有限流器（用于优雅关闭）
func (cfg *Config) StopTickers() {
	cfg.tickerMutex.Lock()
	defer cfg.tickerMutex.Unlock()

	if cfg.NucleiTicker != nil {
		cfg.NucleiTicker.Stop()
		cfg.NucleiTicker = nil
	}

	if cfg.GithubTicker != nil {
		cfg.GithubTicker.Stop()
		cfg.GithubTicker = nil
	}
}

// ReloadTickers 重新加载限流器（配置更新时使用）
func (cfg *Config) ReloadTickers() error {
	// 先停止现有的限流器
	cfg.StopTickers()

	// 重新初始化
	return cfg.initGlobalTickers()
}

// ConfigChange 配置变更类型
type ConfigChange struct {
	Type   string      // 变更类型：database, ratelimit, llm, notifier, http
	Field  string      // 具体字段名
	OldVal interface{} // 旧值
	NewVal interface{} // 新值
}

// ConfigSnapshot 配置快照，用于检测变更
type ConfigSnapshot struct {
	Database DatabaseConfig
	Source   Source
	Model    ModelConfig
	Webhook  WebhookConfig
	GitHub   SourceDetail
	Nuclei   SourceDetail
	CVE      SourceDetail
}

// safeReload 安全的配置重载实现（由智能防抖器调用）
func (cfg *Config) safeReload(e fsnotify.Event) {
	// 使用写锁保证线程安全
	cfg.reloadMutex.Lock()
	defer cfg.reloadMutex.Unlock()

	slog.Info("🔄 开始安全配置重载", "file", e.Name, "op", e.Op.String())
	cfg.lastReload = time.Now()

	// 1. 创建配置快照
	oldSnapshot := cfg.createSnapshot()

	// 2. 验证配置文件
	if err := cfg.validateConfigFile(); err != nil {
		slog.Error("❌ 配置文件验证失败", "error", err)
		return
	}

	// 3. 重新解析配置到临时结构（避免复制互斥锁）
	tempConfig := cfg.createTempConfig()
	if err := cfg.viper.Unmarshal(tempConfig); err != nil {
		slog.Error("❌ 配置解析失败", "error", err)
		return
	}

	// 4. 检测配置变更
	changes := cfg.detectChanges(oldSnapshot, tempConfig)
	if len(changes) == 0 {
		slog.Info("📋 未检测到配置变更")
		return
	}

	// 5. 应用配置变更（只复制配置字段，不复制互斥锁）
	cfg.copyConfigFields(tempConfig)
	if err := cfg.applyChanges(changes); err != nil {
		slog.Error("❌ 应用配置变更失败", "error", err)
		// 尝试恢复旧配置
		cfg.restoreFromSnapshot(oldSnapshot)
		return
	}

	slog.Info("✅ 配置热重载成功完成", "changes", len(changes))
}

// createTempConfig 创建临时配置结构（不包含互斥锁）
func (cfg *Config) createTempConfig() *Config {
	return &Config{
		// 只复制配置字段，不复制互斥锁和运行时字段
		Database: cfg.Database,
		Source:   cfg.Source,
		Model:    cfg.Model,
		Webhook:  cfg.Webhook,

		// 保持运行时字段的引用（不复制）
		LLM:          cfg.LLM,
		Notifier:     cfg.Notifier,
		ctx:          cfg.ctx,
		resty:        cfg.resty,
		viper:        cfg.viper,
		DB:           cfg.DB,
		GithubClient: cfg.GithubClient,
		NucleiClient: cfg.NucleiClient,
		CVEClient:    cfg.CVEClient,
		Watcher:      cfg.Watcher,
		NucleiTicker: cfg.NucleiTicker,
		GithubTicker: cfg.GithubTicker,

		// 不复制互斥锁字段
		// tickerMutex 和 reloadMutex 不复制
		lastReload: cfg.lastReload,
	}
}

// copyConfigFields 安全地复制配置字段（避免复制互斥锁）
func (cfg *Config) copyConfigFields(source *Config) {
	// 只复制配置字段，不复制互斥锁和运行时字段
	cfg.Database = source.Database
	cfg.Source = source.Source
	cfg.Model = source.Model
	cfg.Webhook = source.Webhook

	// 运行时字段保持不变，由重新初始化方法处理
}

// createSnapshot 创建当前配置的快照
func (cfg *Config) createSnapshot() *ConfigSnapshot {
	return &ConfigSnapshot{
		Database: cfg.Database,
		Source:   cfg.Source,
		Model:    cfg.Model,
		Webhook:  cfg.Webhook,
		GitHub:   cfg.Source.GitHub,
		Nuclei:   cfg.Source.Nuclei,
		CVE:      cfg.Source.CVE,
	}
}

// detectChanges 检测配置变更
func (cfg *Config) detectChanges(oldSnapshot *ConfigSnapshot, newConfig *Config) []ConfigChange {
	var changes []ConfigChange

	// 检测数据库配置变更
	if !reflect.DeepEqual(oldSnapshot.Database, newConfig.Database) {
		changes = append(changes, ConfigChange{
			Type:   "database",
			Field:  "Database",
			OldVal: oldSnapshot.Database,
			NewVal: newConfig.Database,
		})
	}

	// 检测限流配置变更
	if oldSnapshot.Nuclei.LimitNum != newConfig.Source.Nuclei.LimitNum ||
		oldSnapshot.GitHub.LimitNum != newConfig.Source.GitHub.LimitNum {
		changes = append(changes, ConfigChange{
			Type:   "ratelimit",
			Field:  "Source.LimitNum",
			OldVal: fmt.Sprintf("Nuclei:%d,GitHub:%d", oldSnapshot.Nuclei.LimitNum, oldSnapshot.GitHub.LimitNum),
			NewVal: fmt.Sprintf("Nuclei:%d,GitHub:%d", newConfig.Source.Nuclei.LimitNum, newConfig.Source.GitHub.LimitNum),
		})
	}

	// 检测模型配置变更
	if !reflect.DeepEqual(oldSnapshot.Model, newConfig.Model) {
		changes = append(changes, ConfigChange{
			Type:   "llm",
			Field:  "Model",
			OldVal: oldSnapshot.Model,
			NewVal: newConfig.Model,
		})
	}

	// 检测通知配置变更
	if !reflect.DeepEqual(oldSnapshot.Webhook, newConfig.Webhook) {
		changes = append(changes, ConfigChange{
			Type:   "notifier",
			Field:  "Webhook",
			OldVal: oldSnapshot.Webhook,
			NewVal: newConfig.Webhook,
		})
	}

	// 检测HTTP客户端配置变更
	if !reflect.DeepEqual(oldSnapshot.GitHub, newConfig.Source.GitHub) ||
		!reflect.DeepEqual(oldSnapshot.Nuclei, newConfig.Source.Nuclei) ||
		!reflect.DeepEqual(oldSnapshot.CVE, newConfig.Source.CVE) {
		changes = append(changes, ConfigChange{
			Type:   "http",
			Field:  "Source.Clients",
			OldVal: "HTTP客户端配置",
			NewVal: "HTTP客户端配置",
		})
	}

	return changes
}

// applyChanges 应用配置变更
func (cfg *Config) applyChanges(changes []ConfigChange) error {
	for _, change := range changes {
		slog.Info("🔧 应用配置变更", "type", change.Type, "field", change.Field)

		switch change.Type {
		case "database":
			if err := cfg.reinitDB(); err != nil {
				slog.Error("❌ 数据库重新初始化失败", "error", err)
				return fmt.Errorf("数据库重新初始化失败: %w", err)
			}
			slog.Info("✅ 数据库配置已更新")

		case "ratelimit":
			if err := cfg.ReloadTickers(); err != nil {
				slog.Error("❌ 限流器重新初始化失败", "error", err)
				return fmt.Errorf("限流器重新初始化失败: %w", err)
			}
			slog.Info("✅ 限流器配置已更新")

		case "llm":
			if err := cfg.reinitLLM(); err != nil {
				slog.Error("❌ LLM重新初始化失败", "error", err)
				return fmt.Errorf("LLM重新初始化失败: %w", err)
			}
			slog.Info("✅ LLM配置已更新")

		case "notifier":
			if err := cfg.reinitNotifier(); err != nil {
				slog.Error("❌ 通知器重新初始化失败", "error", err)
				return fmt.Errorf("通知器重新初始化失败: %w", err)
			}
			slog.Info("✅ 通知器配置已更新")

		case "http":
			cfg.reinitHTTPClients()
			slog.Info("✅ HTTP客户端配置已更新")

		default:
			slog.Warn("⚠️ 未知的配置变更类型", "type", change.Type)
		}
	}

	return nil
}

// validateConfigFile 验证配置文件的有效性
func (cfg *Config) validateConfigFile() error {
	configFile := cfg.viper.ConfigFileUsed()
	if configFile == "" {
		return fmt.Errorf("未找到配置文件")
	}

	// 检查文件是否存在和可读
	if _, err := os.Stat(configFile); err != nil {
		return fmt.Errorf("配置文件不可访问: %w", err)
	}

	return nil
}

// restoreFromSnapshot 从快照恢复配置（错误恢复）
func (cfg *Config) restoreFromSnapshot(snapshot *ConfigSnapshot) {
	slog.Warn("🔄 正在恢复配置到变更前状态")

	// 安全地恢复配置字段（不影响互斥锁）
	cfg.Database = snapshot.Database
	cfg.Source = snapshot.Source
	cfg.Model = snapshot.Model
	cfg.Webhook = snapshot.Webhook

	slog.Info("✅ 配置已恢复到变更前状态")
}

// reinitDB 重新初始化数据库连接
func (cfg *Config) reinitDB() error {
	// 注意：数据库重新初始化需要特别小心
	// 在生产环境中可能需要更复杂的处理逻辑
	slog.Info("🔄 重新初始化数据库连接")

	// 创建新的数据库连接
	if err := cfg.initDB(); err != nil {
		return fmt.Errorf("创建新数据库连接失败: %w", err)
	}

	// 获取新创建的数据库连接进行测试
	newDB := cfg.DB

	// 测试新连接
	sqlDB, err := newDB.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		sqlDB.Close()
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	// 关闭旧连接（如果存在）
	if cfg.DB != nil {
		if oldSqlDB, err := cfg.DB.DB(); err == nil && oldSqlDB != nil {
			oldSqlDB.Close()
		}
	}

	cfg.DB = newDB
	return nil
}

// reinitLLM 重新初始化LLM客户端
func (cfg *Config) reinitLLM() error {
	slog.Info("🔄 重新初始化LLM客户端")

	llm, err := NewLLM(cfg.Model)
	if err != nil {
		return fmt.Errorf("创建LLM客户端失败: %w", err)
	}

	cfg.LLM = llm
	return nil
}

// reinitNotifier 重新初始化通知器
func (cfg *Config) reinitNotifier() error {
	slog.Info("🔄 重新初始化通知器")

	notifier, err := cfg.NewNotifier()
	if err != nil {
		return fmt.Errorf("创建通知器失败: %w", err)
	}

	cfg.Notifier = notifier
	return nil
}

// reinitHTTPClients 重新初始化HTTP客户端
func (cfg *Config) reinitHTTPClients() {
	slog.Info("🔄 重新初始化HTTP客户端")

	// 使用现有的initResty方法重新初始化所有HTTP客户端
	cfg.initResty()
}

// GetConfigReloadStatus 获取配置重载状态（用于监控）
func (cfg *Config) GetConfigReloadStatus() map[string]interface{} {
	cfg.reloadMutex.RLock()
	defer cfg.reloadMutex.RUnlock()

	return map[string]interface{}{
		"last_reload":    cfg.lastReload,
		"config_file":    cfg.viper.ConfigFileUsed(),
		"reload_enabled": cfg.viper != nil,
	}
}

// SmartDebouncer 智能防抖器，用于合并配置变更事件
type SmartDebouncer struct {
	timer          *time.Timer
	mutex          sync.Mutex
	shortDelay     time.Duration   // 短延迟：200ms，用于频繁事件
	longDelay      time.Duration   // 长延迟：2s，用于正常事件
	maxWaitTime    time.Duration   // 最大等待：10s，防止无限延迟
	firstEventTime time.Time       // 第一个事件的时间
	lastEventTime  time.Time       // 最后一个事件的时间
	pendingEvent   *fsnotify.Event // 待处理的事件
	eventCount     int             // 事件计数
}

// initSmartDebouncer 初始化智能防抖器
func (cfg *Config) initSmartDebouncer() {
	cfg.debouncer = &SmartDebouncer{
		shortDelay:  200 * time.Millisecond, // 频繁事件的短延迟
		longDelay:   2 * time.Second,        // 正常事件的长延迟
		maxWaitTime: 10 * time.Second,       // 最大等待时间
	}
}

// smartReload 智能防抖的配置重载入口
func (cfg *Config) smartReload(e fsnotify.Event) {
	if cfg.debouncer == nil {
		// 防抖器未初始化，直接处理
		cfg.safeReload(e)
		return
	}

	cfg.debouncer.mutex.Lock()
	defer cfg.debouncer.mutex.Unlock()

	now := time.Now()

	// 记录事件信息
	if cfg.debouncer.firstEventTime.IsZero() {
		cfg.debouncer.firstEventTime = now
		slog.Debug("📝 记录首次配置变更事件", "file", e.Name, "op", e.Op.String())
	}
	cfg.debouncer.lastEventTime = now
	cfg.debouncer.eventCount++
	cfg.debouncer.pendingEvent = &e

	slog.Debug("📊 配置变更事件统计",
		"file", e.Name,
		"event_count", cfg.debouncer.eventCount,
		"time_since_first", now.Sub(cfg.debouncer.firstEventTime))

	// 停止现有定时器
	if cfg.debouncer.timer != nil {
		cfg.debouncer.timer.Stop()
	}

	// 智能选择延迟时间
	var delay time.Duration

	// 如果事件很频繁（>3次），使用短延迟快速响应
	if cfg.debouncer.eventCount > 3 {
		delay = cfg.debouncer.shortDelay
		slog.Debug("⚡ 检测到频繁事件，使用短延迟", "delay", delay)
	} else {
		delay = cfg.debouncer.longDelay
		slog.Debug("⏱️ 正常事件频率，使用长延迟", "delay", delay)
	}

	// 如果等待时间过长，强制处理（防止无限延迟）
	if now.Sub(cfg.debouncer.firstEventTime) > cfg.debouncer.maxWaitTime {
		delay = 0
		slog.Info("⏰ 达到最大等待时间，立即处理配置变更",
			"max_wait", cfg.debouncer.maxWaitTime,
			"events_merged", cfg.debouncer.eventCount)
	}

	// 设置新的定时器
	cfg.debouncer.timer = time.AfterFunc(delay, func() {
		cfg.executeConfigReload()
	})
}

// executeConfigReload 执行配置重载（防抖器触发）
func (cfg *Config) executeConfigReload() {
	cfg.debouncer.mutex.Lock()
	event := cfg.debouncer.pendingEvent
	eventCount := cfg.debouncer.eventCount
	totalWaitTime := time.Since(cfg.debouncer.firstEventTime)

	// 重置防抖器状态
	cfg.debouncer.firstEventTime = time.Time{}
	cfg.debouncer.lastEventTime = time.Time{}
	cfg.debouncer.eventCount = 0
	cfg.debouncer.pendingEvent = nil
	cfg.debouncer.mutex.Unlock()

	if event != nil {
		slog.Info("🎯 执行配置重载",
			"file", event.Name,
			"events_merged", eventCount,
			"total_wait_time", totalWaitTime,
			"op", event.Op.String())

		// 确保文件稳定后再处理
		if cfg.waitForFileStability(event.Name) {
			cfg.safeReload(*event)
		} else {
			slog.Warn("⚠️ 文件不稳定，跳过此次重载", "file", event.Name)
		}
	}
}

// waitForFileStability 等待文件稳定（防止读取到不完整的文件）
func (cfg *Config) waitForFileStability(filename string) bool {
	const maxRetries = 5
	const retryDelay = 50 * time.Millisecond

	for i := 0; i < maxRetries; i++ {
		if cfg.checkFileStability(filename) {
			slog.Debug("✅ 文件稳定性检查通过", "file", filename, "retries", i)
			return true
		}

		if i < maxRetries-1 {
			slog.Debug("⏳ 等待文件稳定", "file", filename, "retry", i+1)
			time.Sleep(retryDelay)
		}
	}

	slog.Warn("⚠️ 文件稳定性检查超时，继续处理", "file", filename, "max_retries", maxRetries)
	return true // 即使不稳定也继续处理，避免阻塞
}

// checkFileStability 检查文件在短时间内是否稳定
func (cfg *Config) checkFileStability(filename string) bool {
	state1 := cfg.getFileState(filename)
	if state1.modTime.IsZero() {
		return false // 文件不存在或无法访问
	}

	time.Sleep(100 * time.Millisecond)

	state2 := cfg.getFileState(filename)
	if state2.modTime.IsZero() {
		return false // 文件在检查期间被删除
	}

	// 检查修改时间和大小是否稳定
	return state1.modTime.Equal(state2.modTime) && state1.size == state2.size
}

// FileState 文件状态信息
type FileState struct {
	modTime time.Time
	size    int64
}

// getFileState 获取文件状态
func (cfg *Config) getFileState(filename string) FileState {
	info, err := os.Stat(filename)
	if err != nil {
		return FileState{} // 返回零值表示无法获取状态
	}

	return FileState{
		modTime: info.ModTime(),
		size:    info.Size(),
	}
}

// StopDebouncer 停止防抖器（用于优雅关闭）
func (cfg *Config) StopDebouncer() {
	if cfg.debouncer != nil {
		cfg.debouncer.mutex.Lock()
		defer cfg.debouncer.mutex.Unlock()

		if cfg.debouncer.timer != nil {
			cfg.debouncer.timer.Stop()
			cfg.debouncer.timer = nil
		}

		slog.Info("🛑 配置防抖器已停止")
	}
}

// GetDebouncerStatus 获取防抖器状态（用于监控）
func (cfg *Config) GetDebouncerStatus() map[string]interface{} {
	if cfg.debouncer == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	cfg.debouncer.mutex.Lock()
	defer cfg.debouncer.mutex.Unlock()

	return map[string]interface{}{
		"enabled":          true,
		"short_delay":      cfg.debouncer.shortDelay.String(),
		"long_delay":       cfg.debouncer.longDelay.String(),
		"max_wait_time":    cfg.debouncer.maxWaitTime.String(),
		"current_events":   cfg.debouncer.eventCount,
		"has_pending":      cfg.debouncer.pendingEvent != nil,
		"first_event_time": cfg.debouncer.firstEventTime,
		"last_event_time":  cfg.debouncer.lastEventTime,
	}
}

// TaskSchedulingConfig 任务调度配置
type TaskSchedulingConfig struct {
	BatchSize       int    `mapstructure:"batch_size" toml:"batch_size"`               // 批处理大小，默认10
	BatchInterval   string `mapstructure:"batch_interval" toml:"batch_interval"`       // 批次间隔，默认"5s"
	TaskStartDelay  string `mapstructure:"task_start_delay" toml:"task_start_delay"`   // 任务启动延迟，默认"1s"
	EnableRateLimit bool   `mapstructure:"enable_rate_limit" toml:"enable_rate_limit"` // 是否启用任务级别限流，默认true
}

// GetBatchIntervalDuration 获取批次间隔时间
func (tsc *TaskSchedulingConfig) GetBatchIntervalDuration() time.Duration {
	if tsc.BatchInterval == "" {
		return 5 * time.Second
	}
	duration, err := time.ParseDuration(tsc.BatchInterval)
	if err != nil {
		return 5 * time.Second
	}
	return duration
}

// GetTaskStartDelayDuration 获取任务启动延迟时间
func (tsc *TaskSchedulingConfig) GetTaskStartDelayDuration() time.Duration {
	if tsc.TaskStartDelay == "" {
		return 1 * time.Second
	}
	duration, err := time.ParseDuration(tsc.TaskStartDelay)
	if err != nil {
		return 1 * time.Second
	}
	return duration
}

// SetDefaults 设置默认值
func (tsc *TaskSchedulingConfig) SetDefaults() {
	if tsc.BatchSize <= 0 {
		tsc.BatchSize = 10
	}
	if tsc.BatchInterval == "" {
		tsc.BatchInterval = "5s"
	}
	//if tsc.MaxConcurrent <= 0 {
	//	tsc.MaxConcurrent = 3
	//}
	if tsc.TaskStartDelay == "" {
		tsc.TaskStartDelay = "1s"
	}
	// EnableRateLimit 默认为 false，需要显式启用
}
