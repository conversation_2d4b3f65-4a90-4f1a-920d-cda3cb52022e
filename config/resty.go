package config

import (
	"crypto/tls"
	"log/slog"
	"net/url"
	"time"

	"github.com/corpix/uarand"
)

func (cfg *Config) initResty() {
	timeout, err := time.ParseDuration(cfg.Client.Timeout)
	if err != nil || len(cfg.Client.Timeout) == 0 {
		slog.Error("parse timeout", "err", err)
	} else {
		cfg.resty.SetTimeout(timeout)
	}

	if cfg.Client.ProxyConfig.Enabled {
		if _, err = url.Parse(cfg.Client.ProxyConfig.Address); err != nil {
			slog.Error("parse proxy address failed", "err", err)
		}
		cfg.resty.SetProxy(cfg.Client.ProxyConfig.Address)
	}

	if cfg.Client.UserAgent.RandomEnabled {
		cfg.resty.SetHeader("User-Agent", uarand.GetRandom())
	} else {
		cfg.resty.SetHeader("User-Agent", cfg.Client.UserAgent.UserAgent)
	}

	cfg.resty.SetRetryCount(cfg.Client.RetryCount).SetTLSClientConfig(&tls.Config{InsecureSkipVerify: true})
	cfg.GithubClient = cfg.resty.Clone()
	cfg.NucleiClient = cfg.resty.Clone()
	cfg.CVEClient = cfg.resty.Clone()
}
