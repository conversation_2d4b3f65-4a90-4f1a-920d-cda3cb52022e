package config

import (
	"context"
	"fmt"

	"poc-finder/pkg/llm"
)

type LLM interface {
	Query(ctx context.Context, input string) (string, error)
}

func NewLLM(model ModelConfig) (LLM, error) {
	switch model.SelectedModel {
	case "ollama":
		return &llm.Ollama{Model: model.Ollama.ModelName}, nil
	case "openRouter":
		return &llm.OpenRouter{
			Token: model.OpenRouter.Token,
			Model: model.OpenRouter.ModelName,
		}, nil
	}
	return nil, fmt.Errorf("不支持模型 '%s' ", model.SelectedModel)
}
