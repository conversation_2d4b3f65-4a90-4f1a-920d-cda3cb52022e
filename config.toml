title = "poc-finder config"

# 漏洞列表文件路径
vuln-file-path = "./vulnlist.txt"

# resty相关配置
[client]
    timeout = "9s"
    retry-count = 3
    next-retry = 2     # ← 定期查找的间隔（天）
[client.UA]
    random = true
    user-agent = ""
[client.proxy]
    address = "http://127.0.0.1:8080"
    enabled = false

# 各数据源相关配置
# 由于GitHub API 请求限制，所以并发总和不能超过10
[source]
next-retry = 7  # 下次查找的时间间隔，单位为天
github-bearer = "*********************************************************************************************"
[source.github]
    enabled = true
    concurrency = 4
    limit-num = 8 # 请求速率，单位 个/分钟
[source.nuclei]
    enabled = true
    concurrency = 4
    limit-num = 28 # 请求速率，单位 个/分钟
[source.cve]
    enabled = false
    concurrency = 2

# 用于存储数据的数据库信息以及连接语句等
[database]
    type = "postgresql"
    host = "poc_finder_db"
    username = "poc_finder"
    password = "poc_finder"
    dbname = "poc_finder_db"
    port = 5432
    charset = "utf8"
    sslmode = "disable"
    timezone = "Asia/Shanghai"

# 大模型相关配置，包括token、本地ollama配置
[model]
    selected-model = "openRouter"
    concurrency = 1
    [model.openai]
        host = ""
        token = ""
    [model.openRouter]
        token = "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"
        model-name = "google/gemini-2.5-flash-lite"
    [model.wenXin]
        host = ""
        token = ""
    [model.ollama]
        # OLLAMA_HOST
#        host = "***********"
        host = "************"
        token = ""
        model-name = "llama3.1:8b"
# 结果通知
[webhook]
    selected-hook = "DingTalk"
    access-token = "9e9988a0950e87d82d721b028bd591b46f1c523a9f8dcb4e0867c0f5fd99ac82"
    secret = "SECdce4038b82e06dcb1bc532c678b2f0fb13a1a26680bcff40b8408195dcb122cc"

# gRPC服务配置
[grpc]
    enabled = true      # 是否禁用gRPC服务
    host = "0.0.0.0"    # gRPC服务地址
    port = 50051        # gRPC服务端口
    reflection = true   # 是否启用反射服务（便于调试）

# 任务调度相关配置
[task_scheduling]
    batch_size = 1     # 批处理大小，默认1
    batch_interval = "5s"  # 批次间隔，默认"5s"
    task_start_delay = "1s" # 任务启动延迟，默认"1s"
    enable_rate_limit = true # 是否启用任务级别限流，默认true