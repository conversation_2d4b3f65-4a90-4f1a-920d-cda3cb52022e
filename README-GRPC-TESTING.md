# POC-Finder gRPC功能部署和测试指南

## 📋 概述

本指南提供了POC-Finder项目的完整部署和gRPC功能测试方案，包括与WatchVuln的集成测试。

## 🚀 快速开始

### 1. 环境验证
```bash
# 运行部署验证脚本
scripts\validate-deployment.bat
```

### 2. 一键启动
```bash
# 启动POC-Finder gRPC服务
scripts\start-poc-finder.bat
```

### 3. 快速测试
```bash
# 在新终端运行测试
scripts\quick-test.bat
```

## 📁 项目结构

```
poc-finder/
├── cmd/main.go                    # 主程序入口
├── config/config.toml             # 配置文件
├── grpc/                          # gRPC服务实现
│   ├── server.go                  # gRPC服务器
│   └── vuln_handler.go           # 漏洞处理逻辑
├── proto/                         # Protocol Buffers定义
│   ├── vuln_pusher.proto         # 服务定义
│   └── vuln_pusher.pb.go         # 生成的Go代码
├── test/                          # 测试文件
│   └── grpc_test.go              # gRPC单元测试
├── scripts/                       # 部署和测试脚本
│   ├── validate-deployment.bat   # 部署验证
│   ├── start-poc-finder.bat     # 启动脚本
│   ├── quick-test.bat           # 快速测试
│   └── test-grpc.sh             # 详细测试脚本
└── docs/                          # 文档
    ├── deployment-guide.md       # 部署指南
    └── watchvuln-integration.md  # WatchVuln集成指南
```

## 🔧 环境要求

### 必需软件
- **Go 1.19+**: 编译和运行
- **PostgreSQL 12+**: 数据存储
- **grpcurl**: gRPC测试工具

### 安装依赖
```bash
# 安装grpcurl
go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest

# 安装项目依赖
go mod tidy
```

## 🗄️ 数据库设置

### 创建数据库
```sql
-- 连接PostgreSQL
psql -U postgres

-- 创建数据库和用户
CREATE DATABASE poc_finder_db_dev;
CREATE USER poc_finder WITH ENCRYPTED PASSWORD 'poc_finder';
GRANT ALL PRIVILEGES ON DATABASE poc_finder_db_dev TO poc_finder;
```

### 验证连接
```bash
psql -U postgres -d poc_finder_db_dev -h localhost -c "SELECT 1;"
```

## ⚙️ 配置说明

### 关键配置项 (config/config.toml)
```toml
# 数据库配置
[database]
    host = "localhost"
    username = "postgres"
    password = "root"          # 修改为你的密码
    dbname = "poc_finder_db_dev"
    port = 5432

# gRPC服务配置
[grpc]
    enabled = true
    host = "0.0.0.0"
    port = "50051"
    reflection = true

# 数据源配置
[source.github]
    enabled = true
[source.nuclei]
    enabled = true
```

## 🧪 测试场景

### 1. 基础功能测试

#### 服务反射测试
```bash
grpcurl -plaintext localhost:50051 list
```

#### 初始化测试
```bash
grpcurl -plaintext -d '{
  "initial_message": {
    "version": "1.0.0",
    "vuln_count": 5,
    "interval": "1h"
  },
  "timestamp": 1640995200
}' localhost:50051 vuln_pusher.VulnPusher.PushInitial
```

### 2. 漏洞推送测试

#### 普通漏洞（触发所有处理器）
```bash
grpcurl -plaintext -d '{
  "vuln_info": {
    "unique_key": "test-vuln-001",
    "title": "Test Vulnerability",
    "cve": "CVE-2024-0001",
    "severity": "High",
    "tags": ["web", "rce"],
    "from": "test_source"
  },
  "timestamp": 1640995200
}' localhost:50051 vuln_pusher.VulnPusher.PushVuln
```

#### Nuclei模板漏洞（仅触发Nuclei处理器）
```bash
grpcurl -plaintext -d '{
  "vuln_info": {
    "unique_key": "nuclei-template-001",
    "title": "Nuclei Template Vulnerability",
    "cve": "CVE-2024-0002",
    "tags": ["nuclei-templates", "web"],
    "from": "nuclei_source"
  },
  "timestamp": 1640995200
}' localhost:50051 vuln_pusher.VulnPusher.PushVuln
```

### 3. 预期响应
所有成功的请求应返回：
```json
{
  "success": true,
  "message": "处理成功消息"
}
```

## 🔍 日志监控

### 关键日志信息
- `🎯 接收WatchVuln漏洞推送` - 收到漏洞数据
- `✅ 新漏洞记录已存储` - 数据库存储成功
- `🔍 开始POC搜索` - 开始搜索POC
- `🎯 检测到nuclei-templates标签，仅使用Nuclei处理器` - 标签过滤生效
- `🎉 发现POC并已通知` - 找到POC并发送通知

## 🔗 WatchVuln集成

### WatchVuln配置示例
```yaml
# WatchVuln配置文件
grpc:
  enabled: true
  endpoint: "localhost:50051"
  timeout: "30s"

push:
  batch_size: 10
  interval: "1m"
```

### 启动集成测试
```bash
# 1. 启动POC-Finder
./poc-finder.exe grpc --config config/config.toml

# 2. 启动WatchVuln（在WatchVuln目录）
cd C:/Users/<USER>/works/intelli-sec/watchvuln
./watchvuln --config config.yaml
```

## 🐛 故障排查

### 常见问题

#### 1. 编译失败
```bash
# 检查Go版本
go version

# 清理模块缓存
go clean -modcache
go mod tidy
```

#### 2. 数据库连接失败
```bash
# 检查PostgreSQL状态
pg_ctl status

# 测试连接
psql -U postgres -h localhost
```

#### 3. gRPC服务无法启动
```bash
# 检查端口占用
netstat -an | findstr 50051

# 检查配置文件
cat config/config.toml
```

#### 4. grpcurl连接失败
```bash
# 测试基本连接
telnet localhost 50051

# 检查防火墙设置
```

## 📊 性能测试

### 并发测试
```bash
# 发送10个并发请求
for i in {1..10}; do
  grpcurl -plaintext -d '{...}' localhost:50051 vuln_pusher.VulnPusher.PushVuln &
done
wait
```

## ✅ 验证清单

### 部署验证
- [ ] Go环境正常
- [ ] 项目文件完整
- [ ] 依赖安装成功
- [ ] 编译通过
- [ ] 数据库连接正常
- [ ] grpcurl工具可用

### 功能验证
- [ ] gRPC服务启动成功
- [ ] 服务反射正常
- [ ] PushInitial方法正常
- [ ] PushVuln方法正常
- [ ] PushText方法正常
- [ ] 标签过滤功能正常

### 集成验证
- [ ] WatchVuln推送正常接收
- [ ] 漏洞数据正确存储
- [ ] POC搜索功能触发
- [ ] 通知功能正常

## 📚 相关文档

- [详细部署指南](docs/deployment-guide.md)
- [WatchVuln集成指南](docs/watchvuln-integration.md)
- [API文档](proto/vuln_pusher.proto)

## 🆘 获取帮助

如果遇到问题，请：
1. 查看日志输出
2. 运行验证脚本
3. 检查配置文件
4. 参考故障排查指南

---

**注意**: 确保在生产环境中修改默认密码和敏感配置！
