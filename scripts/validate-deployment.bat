@echo off
REM POC-Finder Deployment Validation Script

setlocal enabledelayedexpansion

echo ==========================================
echo POC-Finder Deployment Validation
echo ==========================================

set PASS_COUNT=0
set FAIL_COUNT=0
set TOTAL_TESTS=8

echo [INFO] Starting validation...
echo.

REM 1. Check Go environment
echo [TEST 1/8] Checking Go environment...
go version >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Go not installed or not configured
    set /a FAIL_COUNT+=1
) else (
    echo [PASS] Go environment OK
    set /a PASS_COUNT+=1
)

REM 2. Check project files
echo [TEST 2/8] Checking project files...
if exist "cmd\main.go" (
    if exist "config\config.toml" (
        if exist "proto\vuln_pusher.proto" (
            echo [PASS] Project files complete
            set /a PASS_COUNT+=1
        ) else (
            echo [FAIL] Proto files missing
            set /a FAIL_COUNT+=1
        )
    ) else (
        echo [FAIL] Config file missing
        set /a FAIL_COUNT+=1
    )
) else (
    echo [FAIL] Main program file missing
    set /a FAIL_COUNT+=1
)

REM 3. 检查依赖
echo [TEST 3/8] 检查Go依赖...
go mod tidy >nul 2>&1
if errorlevel 1 (
    call :test_result "FAIL" "Go依赖检查失败"
) else (
    call :test_result "PASS" "Go依赖正常"
)

REM 4. 编译测试
echo [TEST 4/8] 编译测试...
go build -o poc-finder-test.exe cmd/main.go >nul 2>&1
if errorlevel 1 (
    call :test_result "FAIL" "编译失败"
) else (
    call :test_result "PASS" "编译成功"
    del poc-finder-test.exe >nul 2>&1
)

REM 5. 检查PostgreSQL
echo [TEST 5/8] 检查PostgreSQL连接...
psql -U postgres -d postgres -h localhost -c "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    call :test_result "FAIL" "PostgreSQL连接失败"
) else (
    call :test_result "PASS" "PostgreSQL连接正常"
)

REM 6. 检查数据库
echo [TEST 6/8] 检查目标数据库...
psql -U postgres -d poc_finder_db_dev -h localhost -c "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    call :test_result "FAIL" "目标数据库不存在或无法访问"
    echo [INFO] 请运行: CREATE DATABASE poc_finder_db_dev;
) else (
    call :test_result "PASS" "目标数据库正常"
)

REM 7. 检查grpcurl
echo [TEST 7/8] 检查grpcurl工具...
where grpcurl >nul 2>&1
if errorlevel 1 (
    call :test_result "FAIL" "grpcurl未安装"
    echo [INFO] 请运行: go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest
) else (
    call :test_result "PASS" "grpcurl工具可用"
)

REM 8. 检查端口可用性
echo [TEST 8/8] 检查端口50051可用性...
netstat -an | findstr ":50051" >nul 2>&1
if errorlevel 1 (
    call :test_result "PASS" "端口50051可用"
) else (
    call :test_result "FAIL" "端口50051已被占用"
    echo [INFO] 请检查是否有其他服务占用该端口
)

echo.
echo ==========================================
echo 验证结果汇总
echo ==========================================
echo 通过: %PASS_COUNT%/%TOTAL_TESTS%
echo 失败: %FAIL_COUNT%/%TOTAL_TESTS%

if %FAIL_COUNT% equ 0 (
    echo.
    echo [SUCCESS] 🎉 所有验证通过！可以开始部署测试
    echo.
    echo 下一步操作:
    echo 1. 运行: scripts\start-poc-finder.bat
    echo 2. 运行: scripts\quick-test.bat
    echo.
) else (
    echo.
    echo [WARNING] ⚠️  存在 %FAIL_COUNT% 个问题需要解决
    echo.
    echo 请根据上述失败项目进行修复后重新验证
    echo.
)

pause
