# POC-Finder Deployment Validation Script
# PowerShell version for better Unicode support

Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "POC-Finder Deployment Validation" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

$PassCount = 0
$FailCount = 0
$TotalTests = 8

function Test-Result {
    param($Result, $Message)
    if ($Result) {
        Write-Host "[PASS] $Message" -ForegroundColor Green
        $script:PassCount++
    } else {
        Write-Host "[FAIL] $Message" -ForegroundColor Red
        $script:FailCount++
    }
}

Write-Host "[INFO] Starting validation..." -ForegroundColor Blue
Write-Host ""

# Test 1: Check Go environment
Write-Host "[TEST 1/8] Checking Go environment..."
try {
    $goVersion = go version 2>$null
    Test-Result $true "Go environment OK ($goVersion)"
} catch {
    Test-Result $false "Go not installed or not configured"
}

# Test 2: Check project files
Write-Host "[TEST 2/8] Checking project files..."
$mainExists = Test-Path "cmd\main.go"
$configExists = Test-Path "config\config.toml"
$protoExists = Test-Path "proto\vuln_pusher.proto"

if ($mainExists -and $configExists -and $protoExists) {
    Test-Result $true "Project files complete"
} else {
    $missing = @()
    if (-not $mainExists) { $missing += "main.go" }
    if (-not $configExists) { $missing += "config.toml" }
    if (-not $protoExists) { $missing += "vuln_pusher.proto" }
    Test-Result $false "Missing files: $($missing -join ', ')"
}

# Test 3: Check Go dependencies
Write-Host "[TEST 3/8] Checking Go dependencies..."
try {
    go mod tidy 2>$null
    Test-Result $true "Go dependencies OK"
} catch {
    Test-Result $false "Go dependencies check failed"
}

# Test 4: Compilation test
Write-Host "[TEST 4/8] Compilation test..."
try {
    go build -o poc-finder-test.exe cmd/main.go 2>$null
    if (Test-Path "poc-finder-test.exe") {
        Remove-Item "poc-finder-test.exe" -Force
        Test-Result $true "Compilation successful"
    } else {
        Test-Result $false "Compilation failed"
    }
} catch {
    Test-Result $false "Compilation failed"
}

# Test 5: Check PostgreSQL
Write-Host "[TEST 5/8] Checking PostgreSQL connection..."
try {
    $pgResult = psql -U postgres -d postgres -h localhost -c "SELECT 1;" 2>$null
    Test-Result $true "PostgreSQL connection OK"
} catch {
    Test-Result $false "PostgreSQL connection failed"
}

# Test 6: Check target database
Write-Host "[TEST 6/8] Checking target database..."
try {
    $dbResult = psql -U postgres -d poc_finder_db_dev -h localhost -c "SELECT 1;" 2>$null
    Test-Result $true "Target database OK"
} catch {
    Test-Result $false "Target database not accessible"
    Write-Host "[INFO] Please run: CREATE DATABASE poc_finder_db_dev;" -ForegroundColor Yellow
}

# Test 7: Check grpcurl
Write-Host "[TEST 7/8] Checking grpcurl tool..."
try {
    $grpcurlVersion = grpcurl --version 2>$null
    Test-Result $true "grpcurl tool available"
} catch {
    Test-Result $false "grpcurl not installed"
    Write-Host "[INFO] Please run: go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest" -ForegroundColor Yellow
}

# Test 8: Check port availability
Write-Host "[TEST 8/8] Checking port 50051 availability..."
$portInUse = Get-NetTCPConnection -LocalPort 50051 -ErrorAction SilentlyContinue
if ($portInUse) {
    Test-Result $false "Port 50051 is in use"
    Write-Host "[INFO] Please check if another service is using this port" -ForegroundColor Yellow
} else {
    Test-Result $true "Port 50051 available"
}

Write-Host ""
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Validation Summary" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Passed: $PassCount/$TotalTests" -ForegroundColor Green
Write-Host "Failed: $FailCount/$TotalTests" -ForegroundColor Red

if ($FailCount -eq 0) {
    Write-Host ""
    Write-Host "[SUCCESS] All validations passed! Ready to deploy and test" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Run: scripts\start-poc-finder.bat" -ForegroundColor White
    Write-Host "2. Run: scripts\quick-test.bat" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host ""
    Write-Host "[WARNING] $FailCount issues need to be resolved" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please fix the failed items and run validation again" -ForegroundColor White
    Write-Host ""
}

Read-Host "Press Enter to continue"
