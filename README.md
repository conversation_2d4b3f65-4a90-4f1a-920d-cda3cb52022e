poc-finder
==========
用于自动化搜索公开网络中的POC/EXP。
## 功能
- **多数据源搜索**:
  *   从 **GitHub** 仓库中搜索与漏洞相关的项目。
  *   在 **Nuclei-Templates** 项目中精确查找是否存在漏洞的模板。
  *   (预留) CVE 数据源。
- **智能化分析**:
  *   利用 **大语言模型 (LLM)** 分析搜索到的 GitHub 仓库，判断其是否包含真实的 POC/EXP。
  *   支持多种 LLM, 包括 **OpenRouter** 和 **Ollama**。
- **灵活的调度与执行**:
  *   **后台守护模式 (`daemon`)**: 根据数据库中设定的 `next_retry` 时间，自动、定期地执行漏洞搜索任务。
  *   **手动搜索模式 (`search`)**: 允许用户立即对指定的单个或批量漏洞进行搜索。
  *   **文件监控**: 在守护模式下，可以监控漏洞列表文件 (`vuln-file-path`) 的变化，文件更新后自动触发新的扫描任务。
- **丰富的输出与通知**:
  *   **结果导出**: 支持将扫描结果导出为 `xlsx`、`csv` 或 `db` (SQLite) 文件。
  *   **实时通知**: 发现新的 POC/EXP 后，可通过 **钉钉 Webhook** 发送实时通知。
- **数据库集成**:
  *   使用 **PostgreSQL** 数据库存储漏洞信息、扫描记录和下一次尝试时间，实现持久化管理。
  *   支持对数据库中的漏洞信息进行手动修改和更新 (`modify` 命令)。

## 用法
`poc-finder` 通过命令行进行操作，主要包含三个子命令：`search`, `modify`, `daemon`。

### `search` \- 手动搜索漏洞
该命令用于立即执行一次漏洞搜索任务。
**参数:**
*   `--vul-ID`: 指定要查找的漏洞ID (例如: `CVE-2024-xxxx`)。

*   `--vul-name`: 指定要查找的漏洞名称。

*   `--vul-file-path`: 指定一个包含批量漏洞ID或名称的文件路径，每行一个。

*   `--search-type`: 查找模式。如果开启，则仅在数据库中查找历史结果。

*   `--export`: 是否导出查询结果，默认为 `false`。

*   `--export-file-path`: 导出文件的路径 (不含后缀名)。

*   `--export-file-prefix`: 导出文件的格式，支持 `xlsx`, `xls`, `csv`, `db`，默认为 `db`。

**示例:**

Bash
```
#根据漏洞ID搜索./poc-finder search --vul-ID="CVE-2025-27892"#根据漏洞名称批量搜索并导出为xlsx./poc-finder search --vul-file-path="./vulnlist.txt" --export=true --export-file-path="./results" --export-file-prefix="xlsx"
```

### `daemon` \- 后台守护模式
以服务形式在后台运行，根据数据库中的 `next_retry` 字段和文件监控来自动调度扫描任务。

**示例:**
Bash
```
# 启动守护进程./poc-finder daemon
```

### `modify` \- 修改漏洞信息
手动修改数据库中已存储的漏洞信息。

**参数:**
*   `--vul-ID`: 要修改的漏洞ID。
*   `--vul-name`: 要修改的漏洞名称。
*   `--set-has-poc`: 手动设置该漏洞是否存在公开POC/EXP。
*   以及其他用于修改漏洞、仓库、POC文件等信息的参数。

## 配置文件
配置文件为 `config.toml`，用于设置程序的所有行为。

| 配置项            | 子配置项	                                         | 描述                                                             |
|----------------|-----------------------------------------------|----------------------------------------------------------------|
| vuln-file-path | 		                                            | 在daemon模式下监控的漏洞列表文件路径。                                         |
| client         | 		                                            | HTTP客户端相关配置。                                                   |
|                | timeout	                                      | 请求超时时间。                                                        |
|                | retry-count                                   | 	请求失败后的重试次数。                                                   |
|                | UA.random                                     | 	是否使用随机的User-Agent。                                            |
|                | proxy.address                                 | 	代理服务器地址。                                                      |
|                | proxy.enabled                                 | 	是否启用代理。                                                       |
| source         | 		                                            | 数据源配置。                                                         |
|                | next-retry                                    | 	当未找到POC时，下一次重试的间隔天数。                                          |
|                | github-bearer                                 | 	GitHub API 的认证Token。                                          |
|                | github, nuclei, cve                           | 	各数据源的详细配置，包括是否启用(enabled)、并发数(concurrency)和请求速率限制(limit-num)。 |
| database       | 	                                             | PostgreSQL数据库连接信息。                                             |
|                | type, host, port, username, password, dbname	 | 数据库类型、地址、端口、用户名、密码和数据库名。                                       |
| model          | 	 	                                           | 大语言模型(LLM)相关配置。                                                |
|                | selected-model	                               | 选择使用的模型，如 openRouter 或 ollama。                                 |
|                | concurrency                                   | 	LLM请求的并发数。                                                    |
|                | openRouter, ollama 等                          | 	不同LLM提供商的配置，包括Token、API地址(host)和具体模型名称(model-name)。           |
| webhook        | 		                                            | 结果通知配置。                                                        |
|                | selected-hook                                 | 	选择的通知方式，目前支持 DingTalk。                                        |
|                | access-token, secret                          | 钉钉机器人的AccessToken和加签密钥。                                        |

## 技术细节
<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2435.671875 302" style="max-width: 2435.671875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-076fc368-56a4-4565-9945-1391cc817349"><style>#mermaid-076fc368-56a4-4565-9945-1391cc817349{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .error-icon{fill:#a44141;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-thickness-normal{stroke-width:1px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .marker.cross{stroke:lightgrey;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 p{margin:0;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster-label text{fill:#F9FFFE;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster-label span{color:#F9FFFE;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster-label span p{background-color:transparent;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .label text,#mermaid-076fc368-56a4-4565-9945-1391cc817349 span{fill:#ccc;color:#ccc;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node rect,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node circle,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node ellipse,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node polygon,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .rough-node .label text,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node .label text,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .image-shape .label,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .icon-shape .label{text-anchor:middle;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .rough-node .label,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node .label,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .image-shape .label,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .icon-shape .label{text-align:center;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .node.clickable{cursor:pointer;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .arrowheadPath{fill:lightgrey;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster text{fill:#F9FFFE;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .cluster span{color:#F9FFFE;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 rect.text{fill:none;stroke-width:0;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .icon-shape,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .icon-shape p,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-076fc368-56a4-4565-9945-1391cc817349 .icon-shape rect,#mermaid-076fc368-56a4-4565-9945-1391cc817349 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-076fc368-56a4-4565-9945-1391cc817349 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M961.027,40.107L817.783,47.922C674.539,55.738,388.051,71.369,244.807,82.684C101.563,94,101.563,101,101.563,104.5L101.563,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_C_1" d="M961.027,46.282L904.727,53.069C848.426,59.855,735.824,73.427,679.523,83.714C623.223,94,623.223,101,623.223,104.5L623.223,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_D_2" d="M961.027,55.645L937.334,60.871C913.641,66.097,866.254,76.548,842.561,85.274C818.867,94,818.867,101,818.867,104.5L818.867,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_E_3" d="M1011.665,62L1005.034,66.167C998.404,70.333,985.143,78.667,978.513,86.333C971.883,94,971.883,101,971.883,104.5L971.883,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_F_4" d="M1097.593,62L1104.224,66.167C1110.854,70.333,1124.114,78.667,1130.745,86.333C1137.375,94,1137.375,101,1137.375,104.5L1137.375,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_G_5" d="M1148.23,50.887L1183.69,56.906C1219.15,62.925,1290.069,74.962,1325.529,84.481C1360.988,94,1360.988,101,1360.988,104.5L1360.988,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_H_6" d="M1148.23,40.635L1276.6,48.362C1404.97,56.09,1661.71,71.545,1790.079,82.772C1918.449,94,1918.449,101,1918.449,104.5L1918.449,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_I_7" d="M1148.23,38.811L1345.465,46.843C1542.701,54.874,1937.171,70.937,2134.406,82.469C2331.641,94,2331.641,101,2331.641,104.5L2331.641,108"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_B1_8" d="M101.563,166L101.563,170.167C101.563,174.333,101.563,182.667,101.563,192.333C101.563,202,101.563,213,101.563,218.5L101.563,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C1_9" d="M561.793,148.964L518.6,155.97C475.406,162.976,389.02,176.988,345.826,189.494C302.633,202,302.633,213,302.633,218.5L302.633,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C2_10" d="M561.793,165.888L552.231,170.074C542.669,174.259,523.546,182.629,513.984,192.315C504.422,202,504.422,213,504.422,218.5L504.422,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C3_11" d="M684.652,165.888L694.214,170.074C703.776,174.259,722.9,182.629,732.462,192.315C742.023,202,742.023,213,742.023,218.5L742.023,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_C4_12" d="M684.652,148.233L732.076,155.361C779.5,162.489,874.348,176.744,921.771,189.372C969.195,202,969.195,213,969.195,218.5L969.195,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_G1_13" d="M1311.246,156.719L1295.206,162.432C1279.167,168.146,1247.087,179.573,1231.048,190.786C1215.008,202,1215.008,213,1215.008,218.5L1215.008,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_G2_14" d="M1410.73,156.719L1426.77,162.432C1442.81,168.146,1474.889,179.573,1490.929,188.786C1506.969,198,1506.969,205,1506.969,208.5L1506.969,212"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_H1_15" d="M1864.926,159.292L1850.986,164.576C1837.047,169.861,1809.168,180.431,1795.229,191.215C1781.289,202,1781.289,213,1781.289,218.5L1781.289,224"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_H2_16" d="M1971.973,159.292L1985.912,164.576C1999.852,169.861,2027.73,180.431,2041.67,189.215C2055.609,198,2055.609,205,2055.609,208.5L2055.609,212"></path><path marker-end="url(#mermaid-076fc368-56a4-4565-9945-1391cc817349_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_I1_17" d="M2331.641,166L2331.641,170.167C2331.641,174.333,2331.641,182.667,2331.641,192.333C2331.641,202,2331.641,213,2331.641,218.5L2331.641,224"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1054.62890625, 35)" id="flowchart-A-90" class="node default"><rect height="54" width="187.203125" y="-27" x="-93.6015625" style="" class="basic label-container"></rect><g transform="translate(-63.6015625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>poc-finder 根目录</p></span></div></foreignObject></g></g><g transform="translate(101.5625, 139)" id="flowchart-B-91" class="node default"><rect height="54" width="98.515625" y="-27" x="-49.2578125" style="" class="basic label-container"></rect><g transform="translate(-19.2578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="38.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>cmd/</p></span></div></foreignObject></g></g><g transform="translate(623.22265625, 139)" id="flowchart-C-93" class="node default"><rect height="54" width="122.859375" y="-27" x="-61.4296875" style="" class="basic label-container"></rect><g transform="translate(-31.4296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="62.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>handler/</p></span></div></foreignObject></g></g><g transform="translate(818.8671875, 139)" id="flowchart-D-95" class="node default"><rect height="54" width="112.625" y="-27" x="-56.3125" style="" class="basic label-container"></rect><g transform="translate(-26.3125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="52.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>model/</p></span></div></foreignObject></g></g><g transform="translate(971.8828125, 139)" id="flowchart-E-97" class="node default"><rect height="54" width="93.40625" y="-27" x="-46.703125" style="" class="basic label-container"></rect><g transform="translate(-16.703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="33.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>pkg/</p></span></div></foreignObject></g></g><g transform="translate(1137.375, 139)" id="flowchart-F-99" class="node default"><rect height="54" width="137.578125" y="-27" x="-68.7890625" style="" class="basic label-container"></rect><g transform="translate(-38.7890625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>scheduler/</p></span></div></foreignObject></g></g><g transform="translate(1360.98828125, 139)" id="flowchart-G-101" class="node default"><rect height="54" width="99.484375" y="-27" x="-49.7421875" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-19.7421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="39.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>grpc/</p></span></div></foreignObject></g></g><g transform="translate(1918.44921875, 139)" id="flowchart-H-103" class="node default"><rect height="54" width="107.046875" y="-27" x="-53.5234375" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-23.5234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="47.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>proto/</p></span></div></foreignObject></g></g><g transform="translate(2331.640625, 139)" id="flowchart-I-105" class="node default"><rect height="54" width="115.3125" y="-27" x="-57.65625" style="" class="basic label-container"></rect><g transform="translate(-27.65625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="55.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>scripts/</p></span></div></foreignObject></g></g><g transform="translate(101.5625, 255)" id="flowchart-B1-107" class="node default"><rect height="54" width="187.125" y="-27" x="-93.5625" style="" class="basic label-container"></rect><g transform="translate(-63.5625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>main.go - CLI入口</p></span></div></foreignObject></g></g><g transform="translate(302.6328125, 255)" id="flowchart-C1-109" class="node default"><rect height="54" width="115.015625" y="-27" x="-57.5078125" style="" class="basic label-container"></rect><g transform="translate(-27.5078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="55.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>base.go</p></span></div></foreignObject></g></g><g transform="translate(504.421875, 255)" id="flowchart-C2-111" class="node default"><rect height="54" width="188.5625" y="-27" x="-94.28125" style="" class="basic label-container"></rect><g transform="translate(-64.28125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.5625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>github_handler.go</p></span></div></foreignObject></g></g><g transform="translate(742.0234375, 255)" id="flowchart-C3-113" class="node default"><rect height="54" width="186.640625" y="-27" x="-93.3203125" style="" class="basic label-container"></rect><g transform="translate(-63.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="126.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>nuclei_handler.go</p></span></div></foreignObject></g></g><g transform="translate(969.1953125, 255)" id="flowchart-C4-115" class="node default"><rect height="54" width="167.703125" y="-27" x="-83.8515625" style="" class="basic label-container"></rect><g transform="translate(-53.8515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="107.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>cve_handler.go</p></span></div></foreignObject></g></g><g transform="translate(1215.0078125, 255)" id="flowchart-G1-117" class="node default"><rect height="54" width="223.921875" y="-27" x="-111.9609375" style="" class="basic label-container"></rect><g transform="translate(-81.9609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="163.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>server.go - gRPC服务器</p></span></div></foreignObject></g></g><g transform="translate(1506.96875, 255)" id="flowchart-G2-119" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>vuln_handler.go - 漏洞处理器</p></span></div></foreignObject></g></g><g transform="translate(1781.2890625, 255)" id="flowchart-H1-121" class="node default"><rect height="54" width="188.640625" y="-27" x="-94.3203125" style="" class="basic label-container"></rect><g transform="translate(-64.3203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>vuln_pusher.proto</p></span></div></foreignObject></g></g><g transform="translate(2055.609375, 255)" id="flowchart-H2-123" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>vuln_pusher.pb.go - 生成的代码</p></span></div></foreignObject></g></g><g transform="translate(2331.640625, 255)" id="flowchart-I1-125" class="node default"><rect height="54" width="192.0625" y="-27" x="-96.03125" style="" class="basic label-container"></rect><g transform="translate(-66.03125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="132.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>generate_proto.sh</p></span></div></foreignObject></g></g></g></g></g></svg>
1. **启动与调度**:
*   程序通过 `Cobra` 库构建命令行界面。
*   在 `daemon` 模式下，程序会成为一个守护进程。它会首先计算出数据库中所有未找到POC的漏洞中最早的 `next_retry` 时间，并设置一个定时器。
*   同时，它会利用 `watcher` 库监控 `vuln-file-path` 指定的文件。文件内容发生变化会触发一次新的扫描。
*   当定时器到期或文件发生变化时，程序会从数据库或文件中读取需要扫描的漏洞列表，并过滤掉已经找到POC或还未到重试时间的条目。
2. **数据源处理**:
*   程序会并发地从启用的数据源（GitHub, Nuclei）获取信息。每个数据源都有独立的速率限制器 (`rate.Limiter`) 和并发控制器 (`semaphore`)。
*   **GitHub**: 通过GitHub API搜索与漏洞ID或名称相关的仓库。对于每个搜到的仓库，程序会进一步获取其目录树结构和`README.md`文件内容。
*   **Nuclei**: 直接在 `projectdiscovery/nuclei-templates` 仓库中搜索是否存在对应的 `.yaml` 模板文件。
3. **LLM 智能分析**:
*   对于从 GitHub 获取到的每个仓库信息（包括描述、目录结构、README），程序会构造一个特定的 **Prompt**。
*   这个Prompt会请求LLM扮演一名网络安全分析师，判断该仓库是否包含目标漏洞的POC，并要求以固定的格式（Conclusion, POC/EXP File Name, Links, Reason）返回结果。
*   程序随后解析LLM返回的文本，提取出是否有POC (`HasPOC`)、文件名、链接和原因等关键信息。
4. **结果存储与通知**:
*   所有扫描结果，无论是否找到POC，都会通过 `GORM` 存入PostgreSQL数据库。`Vulnerability` 表存储漏洞元信息，`Record` 表存储每次从不同数据源找到的仓库记录。
*   通过 `ON CONFLICT` (Upsert) 语句来插入或更新数据，避免重复记录。
*   如果扫描发现任何一个源确认了POC的存在（`HasPOCOrEXP=true`），程序会通过`DingTalk Notifier`格式化漏洞信息，并调用钉钉机器人的Webhook接口发送Markdown格式的通知。