# POC-Finder

[![Go Version](https://img.shields.io/badge/Go-1.24+-blue.svg)](https://golang.org/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![gRPC](https://img.shields.io/badge/gRPC-1.69+-orange.svg)](https://grpc.io/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org/)

**POC-Finder** 是一个智能化的漏洞概念验证代码（POC/EXP）自动搜索和管理平台。它集成了多数据源搜索、AI智能分析、gRPC服务接口和WatchVuln联动等功能，为安全研究人员和运维团队提供全面的漏洞情报收集解决方案。

## ✨ 核心特性

### 🔍 多数据源智能搜索
- **GitHub 仓库搜索**: 通过GitHub API搜索与漏洞相关的项目和代码
- **Nuclei-Templates 精确匹配**: 在ProjectDiscovery的nuclei-templates项目中查找现成的漏洞模板
- **CVE 数据源**: 预留CVE官方数据源接口（可扩展）
- **智能标签过滤**: 根据漏洞标签自动选择最适合的搜索策略

### 🤖 AI 智能分析
- **多LLM支持**: 集成OpenRouter、Ollama、OpenAI、文心一言等多种大语言模型
- **智能POC识别**: 利用AI分析GitHub仓库内容，准确判断是否包含真实可用的POC/EXP
- **上下文感知**: 基于漏洞描述、严重程度、标签等信息提供精准分析
- **结构化输出**: AI分析结果包含结论、文件名、链接、原因等结构化信息

### 🚀 灵活的执行模式
- **守护进程模式**: 后台持续运行，自动处理定时任务和文件监控
- **手动搜索模式**: 支持单个或批量漏洞的即时搜索
- **gRPC服务模式**: 提供标准gRPC接口，支持WatchVuln等外部系统集成
- **文件监控**: 实时监控漏洞列表文件变化，自动触发增量扫描

### 📊 企业级数据管理
- **PostgreSQL存储**: 使用企业级数据库确保数据可靠性和性能
- **智能去重**: 自动处理重复漏洞，避免资源浪费
- **增量更新**: 支持漏洞信息的增量更新和状态跟踪
- **历史记录**: 完整保存搜索历史和结果变更记录

### 🔄 高级调度系统
- **全局限流控制**: 统一管理各数据源的请求频率，避免API限制
- **智能重试机制**: 基于指数退避算法的失败重试策略
- **批处理优化**: 支持批量处理漏洞任务，提高处理效率
- **配置热重载**: 支持配置文件的实时更新，无需重启服务

### 📢 多渠道通知
- **钉钉Webhook**: 发现POC后立即通过钉钉群通知相关人员
- **结构化通知**: 包含漏洞详情、POC链接、风险等级等完整信息
- **可扩展通知**: 预留接口支持Slack、企业微信等其他通知渠道

## 🏗️ 系统架构

```mermaid
graph TB
    A[WatchVuln/外部系统] -->|gRPC| B[POC-Finder gRPC服务]
    C[命令行接口] --> D[核心引擎]
    B --> D
    D --> E[任务调度器]
    E --> F[多数据源处理器]
    F --> G[GitHub搜索]
    F --> H[Nuclei模板]
    F --> I[CVE数据源]
    G --> J[AI智能分析]
    H --> J
    I --> J
    J --> K[PostgreSQL数据库]
    K --> L[通知系统]
    L --> M[钉钉/Webhook]
    D --> N[文件监控]
    N --> E
```

## 🚀 快速开始

### 环境要求

- **Go**: 1.24+ 
- **PostgreSQL**: 13+
- **操作系统**: Linux/macOS/Windows

### 安装部署

1. **克隆项目**
```bash
git clone https://github.com/your-org/poc-finder.git
cd poc-finder
```

2. **编译项目**
```bash
go build -o poc-finder ./cmd
```

3. **配置数据库**
```sql
-- 创建数据库
CREATE DATABASE poc_finder_db;
-- 创建用户（可选）
CREATE USER poc_finder WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE poc_finder_db TO poc_finder;
```

4. **配置文件**
```bash
cp config.toml.example config.toml
# 编辑配置文件，填入数据库连接信息、API密钥等
```

5. **初始化数据库**
```bash
./poc-finder daemon  # 首次运行会自动创建表结构
```

## 📖 使用指南

### 命令行模式

POC-Finder 提供三个主要子命令：`search`、`modify`、`daemon`。

#### 🔍 search - 手动搜索模式

立即执行漏洞搜索任务，适用于临时查询和测试。

**基本用法:**
```bash
# 根据CVE ID搜索
./poc-finder search --vul-ID="CVE-2024-27892"

# 根据漏洞名称搜索  
./poc-finder search --vul-name="Apache Struts2 RCE"

# 批量搜索（从文件读取）
./poc-finder search --vul-file-path="./vulnlist.txt"
```

**高级选项:**
```bash
# 仅查询历史记录（不进行新搜索）
./poc-finder search --vul-ID="CVE-2024-27892" --search-type=true

# 导出搜索结果
./poc-finder search --vul-file-path="./vulnlist.txt" \
  --export=true \
  --export-file-path="./results" \
  --export-file-prefix="xlsx"
```

**参数说明:**
- `--vul-ID`: 漏洞CVE编号
- `--vul-name`: 漏洞名称或描述
- `--vul-file-path`: 包含漏洞列表的文件路径（每行一个）
- `--search-type`: 是否仅查询历史记录（默认false）
- `--export`: 是否导出结果（默认false）
- `--export-file-path`: 导出文件路径（不含扩展名）
- `--export-file-prefix`: 导出格式（xlsx/csv/db，默认db）

#### 🛠️ modify - 数据管理模式

管理数据库中的漏洞记录，支持批量更新和状态修改。

```bash
# 标记漏洞为已找到POC
./poc-finder modify --vul-ID="CVE-2024-27892" --has-poc=true

# 批量更新重试时间
./poc-finder modify --reset-retry-time
```

#### 🔄 daemon - 守护进程模式

后台持续运行，自动处理定时任务、文件监控和gRPC服务。

```bash
# 启动守护进程（包含gRPC服务）
./poc-finder daemon

# 指定漏洞文件监控
./poc-finder daemon --vuln-file-path="./vulnlist.txt"

# 使用自定义配置文件
./poc-finder daemon --config="./custom-config.toml"
```

**守护进程功能:**
- **定时任务**: 根据数据库中的`next_retry`时间自动执行漏洞搜索
- **文件监控**: 实时监控漏洞列表文件变化，自动触发增量扫描  
- **gRPC服务**: 提供标准gRPC接口，支持WatchVuln等外部系统集成
- **配置热重载**: 监控配置文件变化，自动重载配置
