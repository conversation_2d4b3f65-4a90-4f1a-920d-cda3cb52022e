services:
  poc_finder:
    build: .
    restart: always
    image: poc_finder:latest
    #    command: ["daemon"]
    volumes:
      - "./poc-finder:/app/poc-finder"
      - "./vulnlist.txt:/app/vulnlist.txt"
      - "./config.toml:/app/config.toml"
    depends_on:
      - poc_finder_db

  poc_finder_db:
    image: postgres:14.4-alpine
    restart: 'always'
    environment:
      POSTGRES_DB: poc_finder_db
      POSTGRES_USER: poc_finder
      POSTGRES_PASSWORD: poc_finder
    volumes:
      - "./initdb:/docker-entrypoint-initdb.d"